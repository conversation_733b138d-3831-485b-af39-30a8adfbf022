package skynet.platform;

import skynet.platform.common.shell.Shell;

public class ShellTest {

    public static void main(String[] args) throws Exception {

        String hostname = "**************";
        String username = "";
        String pw = "";
        int port = 22;

        Shell shell = Shell.build(hostname, port, username, pw, 5);
        String[] commands = new String[]{"sh $SKYNET_HOME/bin/ant-xagent.sh", "sh $SKYNET_HOME/bin/ant-xagent.sh"};
        String response = shell.execCmd(commands);
        System.out.println(response);
        shell.close();
    }
}