package skynet.platform.agent;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.platform.common.domain.BootParam;
import skynet.platform.common.domain.HealthParam;
import skynet.platform.common.domain.UpdateParam;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(SpringRunner.class)
public class BootParamTest {

    /**
     * 具体的业务测试
     *
     * @throws Exception
     */
    @Test
    public void test() throws Exception {

        BootParam bootParam = new BootParam();
        bootParam.setWorkHome("/iflytek/server/skynet");
        bootParam.setWorkCmd("java");
        bootParam.setWorkArgs(Arrays.asList("-Xmx2048m", "-Xms2048m", "-jar", "gis-service-1.0.0-SNAPSHOT.jar"));
        bootParam.getWorkEnvs().put("ENGINE_HOME", "xxxx");
        bootParam.setKillSignal(15);
        bootParam.setLogCollection(true);
//        bootParam.setMeshEnabled(true);

        HealthParam healthParam = new HealthParam();

        List<UpdateParam> updateParams = Collections.singletonList(new UpdateParam("/iflytek/server/skynet_test", "http://**************/file.zip", "", ""));

        bootParam.setHealthParam(healthParam);
        bootParam.setUpdateParams(updateParams);

        String s = JSON.toJSONString(bootParam, JSONWriter.Feature.PrettyFormat);

        System.err.println(s);
    }
}