package skynet.platform.agent.logging.appender;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import com.github.loki4j.logback.AbstractLoki4jEncoder;
import com.github.loki4j.logback.ApacheHttpSender;
import com.github.loki4j.logback.JsonEncoder;
import com.github.loki4j.logback.Loki4jAppender;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.common.OsUtil;
import skynet.platform.agent.logging.config.LokiProperties;

@Slf4j
public class LokiLoggerAppender implements SkynetLoggerAppender {

    private final Loki4jAppender loki4jAppender;

    public LokiLoggerAppender(LokiProperties lokiProperties) {
        ApacheHttpSender apacheHttpSender = new ApacheHttpSender();
        apacheHttpSender.setUrl(String.format("http://%s/loki/api/v1/push", lokiProperties.getHost()));
        log.info(" loki4jAppender url={}", apacheHttpSender.getUrl());
        this.loki4jAppender = new Loki4jAppender();
        this.loki4jAppender.setContext(((Logger) log).getLoggerContext());
        AbstractLoki4jEncoder.LabelCfg label = new AbstractLoki4jEncoder.LabelCfg();
        String hostName = OsUtil.getHostName();
        label.setPattern("host=" + hostName + ",logger=%logger,level=%level,thread=%thread");
        AbstractLoki4jEncoder encoder = new JsonEncoder();
        encoder.setLabel(label);
        encoder.setSortByTime(true);
        this.loki4jAppender.setFormat(encoder);
        this.loki4jAppender.setHttp(apacheHttpSender);
    }

    @Override
    public Appender<ILoggingEvent> getAppender() {
        return this.loki4jAppender;
    }
}

//<http class="com.github.loki4j.logback.ApacheHttpSender">
//<url>http://${LOKI_HOST:-127.0.0.1:3100}/loki/api/v1/push</url>
//</http>
//<format>
//<label>
//<pattern>host=${HOSTNAME},logger=%logger,level=%level,action=%X{actionId},pid=%X{pid}
//</pattern>
//</label>
//<message>
//<pattern>%msg %ex</pattern>
//</message>
//<sortByTime>true</sortByTime>
//</format>
