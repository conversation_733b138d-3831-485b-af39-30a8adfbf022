package skynet.platform.agent.logging.config;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SocketProperties extends Jsonable {

    private boolean enabled = true;
    private String host = "127.0.0.1";
    private int port = 4560;

    @Override
    public String toString() {
        return super.toString();
    }
}
