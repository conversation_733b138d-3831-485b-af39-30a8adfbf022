package skynet.platform.agent.logging.endpoint;

import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import skynet.platform.agent.logging.config.LogstashProperties;
import skynet.platform.agent.logging.config.LokiProperties;
import skynet.platform.agent.logging.config.SocketProperties;
import skynet.platform.agent.logging.server.SocketAppenderServer;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */

@Endpoint(id = "skynet-logging")
public class LoggingSocketEndpoint {


    private final LogstashProperties logstashProperties;
    private final LokiProperties lokiProperties;

    private final SocketProperties socketProperties;

    @Autowired(required = false)
    private SocketAppenderServer socketAppenderServer;

    public LoggingSocketEndpoint(LogstashProperties logstashProperties,
                                 LokiProperties lokiProperties,
                                 SocketProperties socketProperties) {
        this.socketProperties = socketProperties;
        this.lokiProperties = lokiProperties;
        this.logstashProperties = logstashProperties;
    }

    @ReadOperation
    public Object invoke() {
        Map<String, Object> status = new TreeMap<>();
        status.put("socketProperties", socketProperties);
        status.put("logstashProperties", logstashProperties);
        status.put("lokiProperties", lokiProperties);
        if (socketAppenderServer != null) {
            status.put("socketAppenderServer", socketAppenderServer);
        }

        return JSON.toJSONString(status);
    }

}
