package skynet.platform.agent.logging.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import skynet.platform.agent.logging.ConsoleLoggerCommandLineRunner;
import skynet.platform.agent.logging.appender.LogstashLoggerAppender;
import skynet.platform.agent.logging.appender.LokiLoggerAppender;
import skynet.platform.agent.logging.appender.SkynetLoggerAppender;
import skynet.platform.agent.logging.endpoint.LoggingSocketEndpoint;
import skynet.platform.agent.logging.server.SocketAppenderServer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11 16:30
 */
@Configuration
public class LoggingAutoConfiguration {


    @Bean
    @ConfigurationProperties("skynet.logging.logstash")
    public LogstashProperties logstashProperties() {
        return new LogstashProperties();
    }

    @Bean
    @ConfigurationProperties("skynet.logging.loki")
    public LokiProperties lokiProperties() {
        return new LokiProperties();
    }


    @Bean
    @ConfigurationProperties("skynet.logging.socket")
    public SocketProperties socketProperties() {
        return new SocketProperties();
    }

    @Bean
    @ConditionalOnProperty(value = "skynet.logging.logstash.enabled")
    public LogstashLoggerAppender logstashLoggerAppender(LogstashProperties logstashProperties) {
        return new LogstashLoggerAppender(logstashProperties);
    }

    @Bean
    @ConditionalOnProperty(value = "skynet.logging.loki.enabled")
    public LokiLoggerAppender lokiLoggerAppender(LokiProperties lokiProperties) {
        return new LokiLoggerAppender(lokiProperties);
    }

    /**
     * 缺省 是开启SocketAppenderServer
     *
     * @param socketProperties
     * @return
     */
    @Bean
    @ConditionalOnProperty(value = "skynet.logging.socket.enabled", matchIfMissing = true)
    public SocketAppenderServer socketAppenderServer(SocketProperties socketProperties, List<SkynetLoggerAppender> appenderList) {
        return new SocketAppenderServer(socketProperties, appenderList);
    }

    @Bean
    public LoggingSocketEndpoint loggingSocketEndpoint(LogstashProperties logstashProperties, LokiProperties lokiProperties, SocketProperties socketProperties) {
        return new LoggingSocketEndpoint(logstashProperties, lokiProperties, socketProperties);
    }

    @Bean
    @ConditionalOnProperty(value = "skynet.logging.console.logger.test.enabled")
    public ConsoleLoggerCommandLineRunner consoleLoggerCommandLineRunner(Environment environment) {
        return new ConsoleLoggerCommandLineRunner(environment);
    }
}
