package skynet.platform.agent.grafana.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;


@Slf4j
@ConditionOnGrafana
@Configuration(proxyBeanMethods = false)
public class RestTemplateBeanConfig {
    public static final String BEAN_NAME = "skynet.platform.grafana.service.RestTemplate";

    @Bean(BEAN_NAME)
    public RestTemplate restTemplate(@Qualifier("authRestTemplate") RestTemplate authRestTemplate) {
//        authRestTemplate.setRequestFactory(clientHttpRequestFactory());
        return authRestTemplate;
    }

    //TODO:
//    @Bean
//    public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() {
//        try {
//            HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
//            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (arg0, arg1) -> {
//                // TODO Auto-generated method stub
//                return true;
//            }).build();
//            httpClientBuilder.setSSLContext(sslContext);
//            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
//            SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
//            // 注册http和https请求
//            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.getSocketFactory())
//                    .register("https", sslConnectionSocketFactory).build();
//            // 开始设置连接池
//            PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
//            // 最大连接数
//            poolingHttpClientConnectionManager.setMaxTotal(100);
//            // 同路由并发数
//            poolingHttpClientConnectionManager.setDefaultMaxPerRoute(5);
//            httpClientBuilder.setConnectionManager(poolingHttpClientConnectionManager);
//            // httpClientBuilder.setRetryHandler(new DefaultHttpRequestRetryHandler(2, true));// 重试次数
//            HttpClient httpClient = httpClientBuilder.build();
//            // httpClient连接配置
//            HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
//            // 连接超时
//            clientHttpRequestFactory.setConnectTimeout(20000);
//            // 数据读取超时时间
////            clientHttpRequestFactory.setReadTimeout(20000);
//            // 连接不够用的等待时间
//            clientHttpRequestFactory.setConnectionRequestTimeout(10000);
//            return clientHttpRequestFactory;
//        } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
//            log.error(e.getMessage());
//        }
//        return null;
//    }

}
