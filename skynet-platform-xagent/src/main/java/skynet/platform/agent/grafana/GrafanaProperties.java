package skynet.platform.agent.grafana;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;

/**
 * grafana相关配置
 *
 * <AUTHOR>
 * @since 2018年12月11日 上午10:13:15
 */
@Getter
@Setter
@ConditionOnGrafana
@Component
@RefreshScope
public class GrafanaProperties {

    /**
     * grafana服务坐标
     */
    @Value("${skynet.grafana.actionPoint:grafana-server-v6@ant-mon}")
    private String grafanaActionPoint;

    /**
     * Prometheus 服务坐标
     */
    @Value("${skynet.prometheus.actionPoint:prometheus@ant-mon}")
    private String prometheusActionPoint;

    /**
     * prometheus 用户
     */
    @Value("${skynet.prometheus.user:admin}")
    private String prometheusUser;

    /**
     * prometheus 密码
     */
    @Value("${skynet.prometheus.password:skynet2230}")
    private String prometheusPwd;

    /**
     * Loki 是否启用
     */
    @Value("${skynet.logging.loki.enabled:false}")
    private boolean LokiEnabled;

    /**
     * Loki 服务地址 127.0.0.1:3100
     */
    @Value("${skynet.logging.loki.host:}")
    private String LokiHost;

    /**
     * loki 用户
     */
    @Value("${skynet.logging.loki.user:admin}")
    private String LokiUser;

    /**
     * loki 密码
     */
    @Value("${skynet.logging.loki.password:skynet2230}")
    private String LokiPwd;

    /**
     * Prometheus 服务坐标
     */
    @Value("${skynet.xmanager.actionPoint:ant-xmanager@ant}")
    private String xmanagerActionPoint;

    /**
     * grafana用户
     */
    @Value("${skynet.grafana.user:admin}")
    private String grafanaUser;

    /**
     * grafana密码
     */
    @Value("${skynet.grafana.password:skynet}")
    private String grafanaPassword;

    /**
     * grafana主题风格（默认为light)
     */
    @Value("${skynet.grafana.theme:light}")
    private String grafanaTheme;

    /**
     * grafana dashboard json 文件正则表达式
     */
    @Value("${skynet.grafana.dashboardName.regex:dashboard-.+\\.json}")
    private String dashboardNameRegex;

    /**
     * elasticsearch 服务主机名或ip地址
     */
    @Value("${skynet.logging.es.host:}")
    private String esHost;

    /**
     * elasticsearch http服务端口
     */
    @Value("${skynet.logging.es.port.http:0}")
    private Integer esPortHttp;

    /**
     * elasticsearch 用户
     */
    @Value("${skynet.logging.es.user:}")
    private String esUser;

    /**
     * elasticsearch 密码
     */
    @Value("${skynet.logging.es.password:}")
    private String esPwd;

    @Value("${skynet.grafana.load.interval:90}")
    private int loadInterval;

    @Value("${skynet.grafana.cache.ttl:5000}")
    private int queryCacheTTL;

    @Value("${skynet.grafana.table.maxlen:50}")
    private int tableMaxLen;
}
