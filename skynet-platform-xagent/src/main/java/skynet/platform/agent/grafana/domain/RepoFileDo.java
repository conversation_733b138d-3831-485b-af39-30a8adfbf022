package skynet.platform.agent.grafana.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class RepoFileDo extends Jsonable {

    private String plugin;
    private String fileName;
    private String filePath;
    private boolean isDirectory;
    private long fileSize;
    private String lastUpdateTime;
    private String createTime;
    private String md5sum;

    @Override
    public String toString() {
        return super.toString();
    }
}