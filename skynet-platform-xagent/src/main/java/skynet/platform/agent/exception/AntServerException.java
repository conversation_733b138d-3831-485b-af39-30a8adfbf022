package skynet.platform.agent.exception;

import java.io.Serial;

/**
 * <AUTHOR>
 */
public class AntServerException extends RuntimeException {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 133242424423L;

    /**
     * Constructor
     */
    public AntServerException() {

    }

    /**
     * Constructor
     *
     * @param message A message to include with the exception.
     */
    public AntServerException(String message) {
        super(message);

    }

    public AntServerException(String format, Object... args) {
        this(String.format(format, args));

    }

    /**
     * Constructor
     *
     * @param message        A message to include with the exception.
     * @param innerException A nested exception to include.
     */
    public AntServerException(String message, Exception innerException) {

        super(message, innerException);

    }

    public AntServerException(Exception innerException, String msgFormat, Object... args) {

        super(String.format(msgFormat, args), innerException);

    }
}
