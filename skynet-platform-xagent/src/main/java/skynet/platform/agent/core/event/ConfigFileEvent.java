package skynet.platform.agent.core.event;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.SkynetProperties;
import skynet.boot.security.SkynetEncryption;
import skynet.platform.agent.core.boot.BaseBoot;
import skynet.platform.common.domain.BootProfile;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuilder;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConfigFileEvent implements BootEvent {

    private final BootEnvironmentBuilder bootEnvironmentBuilder;
    private final SkynetEncryption skynetEncryption;

    public ConfigFileEvent(BootEnvironmentBuilder bootEnvironmentBuilder, SkynetEncryption skynetEncryption) {
        this.bootEnvironmentBuilder = bootEnvironmentBuilder;
        this.skynetEncryption = skynetEncryption;
    }

    @Override
    public void onEvent(BaseBoot baseBoot) throws Exception {
        BootProfile bootProfile = baseBoot.getBootParam();
        //构建完整 App属性配置（用户自定义 + skynet内置的 + skynet 环境变量）
        build(bootProfile, baseBoot.getAppEnvironment(), baseBoot.getAppConfigFile(), baseBoot.getAppPort());
    }

    private void build(BootProfile bootProfile, BootEnvironment bootEnvironment, File targetFile, int port) throws Exception {
        log.debug("SavePropFile, file={} begin", targetFile);
        if (bootEnvironment == null) {
            log.warn("the bootEnvironment is null");
            SkynetProperties skynetProperties = new SkynetProperties(bootEnvironmentBuilder.getEnvironment());
            skynetProperties.setActionPoint(bootProfile.getFullName());
            skynetProperties.setActionId(bootProfile.getAid());
            skynetProperties.setPort(port);
            bootEnvironment = bootEnvironmentBuilder.build(skynetProperties);
        }

        for (Map.Entry<String, Object> item : bootEnvironment.entrySet()) {
            if (item.getValue() != null && item.getValue().toString().startsWith("{cipher}")) {
                try {
                    item.setValue(skynetEncryption.decrypt(item.getValue().toString().replace("{cipher}", "")));
                } catch (Exception e) {
                    log.error("Decrypt error:{}", e.getMessage());
                }
            }
        }
        String props = bootEnvironment.toPropLines();
        log.debug("SavePropFile, file={} begin", targetFile);
        FileUtils.write(targetFile, props, StandardCharsets.UTF_8);
    }

    public void destroy(BootStatus bootStatus) throws IOException {
        //在debug模式下 不清理，保留现场，方便排查问题
        if (!bootStatus.isDebug()) {
            del(bootStatus.getAppConfigFile());
        }
    }

    private void del(String configFileName) throws IOException {
        //删除配置文件
        if (StringUtils.isNoneBlank(configFileName)) {
            File configFile = new File(configFileName);
            if (configFile.exists()) {
                org.codehaus.plexus.util.FileUtils.deleteDirectory(configFile);
            }
        }
    }
}
