package skynet.platform.agent.core.controller;


import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.boot.websocket.WebSocketConfig;
import skynet.platform.agent.core.HealthService;
import skynet.platform.agent.core.domain.HealthResult;
import skynet.platform.agent.core.domain.ObservableQueue;

import java.io.IOException;
import java.util.Observer;

/**
 * Health日志推送服务
 *
 * <pre>
 * // ws://127.0.0.1:8080/skynet/agent/health/mq-vspp-v1_0@engine_1
 * </pre>
 *
 * <AUTHOR> [2017年12月1日 上午11:12:33]
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@ServerEndpoint(value = "/skynet/agent/health/{antId}", configurator = HttpSessionConfigurator.class)
public class HealthWebsocket {

    private final HealthService healthService;

    private String antId;
    private ObservableQueue<HealthResult> observableQueue;
    private Observer observer;
    private volatile boolean isPause = false;

    public HealthWebsocket(HealthService healthService) {
        this.healthService = healthService;
    }

    @OnOpen
    public void onOpen(@PathParam("antId") String antId, Session session, EndpointConfig config) throws IOException {
        this.antId = antId;

        log.info("New Connected [sessionId={}][from={}][AntId={}] ... ", session.getId(), WebSocketConfig.getRemoteAddressBySession(session), antId);

        // session.getBasicRemote().sendText(String.format("[agent connected.][%s]", antId));

        observableQueue = healthService.getQueue(antId);
        if (observableQueue == null) {
            session.getBasicRemote().sendText(String.format("[the antId [%s] is not exist] ", antId));
            session.close();
        }

        // 把已经有的日志输出
        for (HealthResult item : observableQueue.getList()) {
            session.getBasicRemote().sendText(item.toString());
        }

        this.observer = (o, arg) -> {
            if (arg != null) {
                try {
                    if (!isPause) {
                        session.getBasicRemote().sendText(arg.toString());
                    }
                } catch (IOException e) {
                    log.info("send text error={}", e.getMessage());
                }
            }
        };
        this.observableQueue.addObserver(observer);
    }

    // 收到消息时执行
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("AntId [{}] sessionId[{}] : message[{}]", antId, session.getId(), message);
        this.isPause = "pause".equals(message);
    }

    // 连接关闭时执行
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.debug("AntId [{}] Session {} closed because of {}", antId, session.getId(), closeReason);
        if (observableQueue != null) {
            observableQueue.deleteObserver(observer);
        }

    }

    // 连接错误时执行
    @OnError
    public void onError(Session session, Throwable t) {
        try {
            log.debug("AntId [{}] AgentWebsocket  onError.{}", antId, t.getMessage());
            if (observableQueue != null) {
                observableQueue.deleteObserver(observer);
            }
        } catch (Exception e) {
            log.error("close error.");
        }
    }
}
