
<h1>服务管理</h1>

<!-- TOC -->

- [1. 服务分配](#1-服务分配)
  - [1.2.1. 单服务器服务分配](#121-单服务器服务分配)
  - [1.2.2. 多服务器服务分配](#122-多服务器服务分配)
- [2. 运行状态](#2-运行状态)
- [3. 服务启停](#3-服务启停)
  - [3.1. 服务启动](#31-服务启动)
  - [3.2. 服务停止](#32-服务停止)
  - [3.3. 服务重启](#33-服务重启)

<!-- /TOC -->

# 1. 服务分配

可以将服务分配理解为"服务部署", 是将一个或多个服务实例与一个物理节点绑定的过程。

## 1.2.1. 单服务器服务分配

在要分配服务的服务器功能区域右侧点击“操作”，在下拉选项中选择“服务分配”


![alt](res/single-server-allocate-1.png)

弹出对话框中，选择要分配服务所属的应用系统，点击服务右侧的加号按钮，即可将服务添加到服务列表中。可以调整服务的实例数。

![alt](res/single-server-allocate-2.png)

> 说明：单物理节点多实例部署的服务，必须要保证端口号不冲突，通常将`'服务端口'`设置为0(由skynet-xagent随机生成)。多实例部署的web应用，可以通过skynet提供的Nginx网关访问。

## 1.2.2. 多服务器服务分配

可以同时在多个服务器上分配一批相同的服务。

![alt](res/multi-server-allocate-1.png)

# 2. 运行状态

![alt](res/service-status.png)

被分配到server节点的服务有四种状态，对应四种不同的图示颜色

|颜色|状态|含义|
|-|-|-|
|黄色|loading|服务启动中|
|蓝色|up|服务正常（最近一次健康状态检查成功）|
|红色|down|服务异常（健康状态检查失败）|
|灰色|disabled|服务被禁用(停止)|



# 3. 服务启停

## 3.1. 服务启动
当服务第一次被分配到某个节点上时，自动启动。

当服务为禁用状态，可以右键单击服务图标选择`"启用"`, 启动服务。

![alt](res/service-start.png)



## 3.2. 服务停止

当服务被移除分配列表时，服务被停止。

当服务为非禁用状态，可以右键单击服务图标选择`"禁用服务"`。

![alt](res/service-stop.png)

## 3.3. 服务重启

当服务状态正常时，可以右键单击服务图标选择`"重启服务"`。

![alt](res/service-restart.png)