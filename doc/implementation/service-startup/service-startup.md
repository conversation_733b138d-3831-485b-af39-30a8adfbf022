<h1>服务启停</h1>

# 2.1.1009.1之前版本的启停流程

2.1.1009.1之前的版本，skynet启动服务是直接在java程序里使用jdk的Runtime.java fork出Process启动被托管服务的。这种启动方式有很大的弊端：
* 被托管服务进程的父进程是skynet-xagent，只要skynet-xagent停止，所有的被托管服务进程都会停止，两者之间存在严重的耦合。
* 当被托管服务启动失败时，很难排查失败的原因，因为启动过程无法手动复盘。

2.1.1009.1之前的版本，skynet直接通过Process停止被托管服务。


# 2.1.1009.1及以后版本的启停流程

从2.1.1009.1版本开始，skynet改进了启动服务的方式，如下图

![alt](res/startup-architechture.png)

skynet-xagent启动服务之前，会在 `'/dev/shm/.skynet/action/${SKYNET_ACTION_ID}'` 目录下生成一堆脚本和文件

* `start.sh` ： skynet-xagent直接调用的脚本，用来执行启动流程。它负责启动stream.py和target.sh两个脚本，并将这两个进程的pid记录到文件里。
* `stop.sh` : skynet-xagent直接调用的脚本，用来执行停止流程。它负责停止stream.py脚本，以及以target.sh为父进程的整个进程树。
* `target.sh` ：真正执行服务启动命令行的脚本，它由skynet-xagent根据服务定义动态生成。
* fifo : Linux命名管道文件，用来传输被托管服务的标准输出流
* `stream.py` : start.sh启动target.sh时，将后者的标准输出流重定向给stream.py进程，stream.py从标准输入流中读取字符串然后写入到fifo管道中。
* stream.pid / target.pid : 用来记录pid的文件
* `show_pid.sh` ：手动执行的脚本，方便查看所有相关进程运行情况。

skynet-xagent先根据服务定义装配出`target.sh` ，这里面包含环境变量的定义和启动服务用的命令行，然后执行`start.sh`。`start.sh` 负责在后台启动`target.sh`和`stream.py`，将这两个进程的pid记录到相应的pid文件。`start.sh`将`target.sh`的标准输出和错误输出重定向到了`stream.py`，`stream.py`循环读取标准输入流并将读取到的内容写入fifo。 skynet-xagent在执行完`start.sh`后，创建一个线程循环从fifo中读取InputStream。

为什么需要fifo和`stream.py`？ 为了将skynet-xagent和被托管服务解耦，它们不能是父子进程关系，那么skynet-xagent为了获取被托管服务的标准输出流，必须借助一个队列。Linux天然地提供了一个管道机制，使用mkfifo命令可以方便地创建一个先进先出队列（命名管道），它实际上是系统内核的一块缓冲区，读这个文件的程序就是队列的消费者，写这个文件的程序就是队列的生产者。命名管道有个特性，当不存在读取这个文件的程序时，写入文件的程序的write调用会报IO错误，如果利用重定向直接将target.sh的输出重定向到fifo，那么当skyent-xagent停止时，target.sh会报错退出。所以我们需要stream.py负责中继，它能捕捉并忽略IO异常，保证target.sh的标准输出永远被尝试发送到队列里。 

当需要停止服务时，skynet-xagent调用`stop.sh`。该脚本读取pid文件，kill stream.pid,并查找从target.pid派生出的整个进程树，kill该树上的所有pid。

设计这一套机制，有如下好处：
* skynet-xagent与被托管服务完全解耦，xagent停止也不影响服务运行。xagent重启后依然能继续获取服务的标准输出流。
* 当服务启动失败时，手动执行`target.sh`，即可排查失败原因，极大降低了故障排查的难度。