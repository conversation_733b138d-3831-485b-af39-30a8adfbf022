package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sHorizontalPodAutoscaler;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sHorizontalPodAutoscalerService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sHorizontalPodAutoscalerController implements V3K8sHorizontalPodAutoscaler {

    private final K8sHorizontalPodAutoscalerService k8sHorizontalPodAutoscalerService;

    public K8sHorizontalPodAutoscalerController(K8sHorizontalPodAutoscalerService k8sHorizontalPodAutoscalerService) {
        this.k8sHorizontalPodAutoscalerService = k8sHorizontalPodAutoscalerService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getHorizontalPodAutoscalers(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sHorizontalPodAutoscalerService.getHorizontalPodAutoscalers(ip, k8sQuery);
        response.setData(results);
        log.debug("getHorizontalPodAutoscalers response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sHorizontalPodAutoscalerService.getHorizontalPodAutoscaler(ip, namespace, hpaName);
        response.setData(result);
        log.debug("getHorizontalPodAutoscaler response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getHorizontalPodAutoscalerYaml(String ip, String namespace, String hpaName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sHorizontalPodAutoscalerService.getHorizontalPodAutoscalerYaml(ip, namespace, hpaName);
        response.setData(result);
        log.debug("getHorizontalPodAutoscalerYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S HPA 管理", operation = "删除 HPA", message = "ip=#{#ip}, namespace=#{#namespace}, hpaName=#{#hpaName}")
    public SkynetApiResponse<JSONObject> deleteHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sHorizontalPodAutoscalerService.deleteHorizontalPodAutoscaler(ip, namespace, hpaName);
        response.setData(result);
        log.debug("deleteHorizontalPodAutoscaler response:{}", response);
        return response;
    }

}
