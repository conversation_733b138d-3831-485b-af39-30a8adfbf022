package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sNodeMetricService {

    /**
     * 获取 NodeMetric 列表
     */
    List<JSONObject> getNodeMetrics(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 NodeMetric 详情
     */
    JSONObject getNodeMetric(String ip, String nodeName) throws Exception;
}
