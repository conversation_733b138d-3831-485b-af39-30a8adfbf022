package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.models.V1StatefulSet;
import io.kubernetes.client.openapi.models.V1StatefulSetList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.PatchUtils;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateReplica;
import skynet.platform.feign.model.K8sUpdateStatefulSetStrategy;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sStatefulSetService;

import java.util.ArrayList;
import java.util.List;

/**
 * Kubernetes StatefulSet 服务实现类
 * <p>
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 移除了已弃用的 PatchUtils.patch 方法，改为直接调用 API
 * - 更新了所有 patch 操作以使用新的 API 模式
 * - 保持原有功能的完整性和兼容性
 */
@Slf4j
@Service
public class K8sStatefulSetServiceImpl extends K8sWorkloadService implements K8sStatefulSetService {

    public K8sStatefulSetServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 StatefulSet 列表
     */
    @Override
    public List<JSONObject> getStatefulSets(String ip, K8sQuery k8sQuery) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        List<JSONObject> statefulSetDtoList = new ArrayList<>();
        V1StatefulSetList statefulSetList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            statefulSetList = api.listNamespacedStatefulSet(k8sQuery.getNamespace()).execute();
        } else {
            statefulSetList = api.listStatefulSetForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1StatefulSet> filteredStatefulSets = applyK8sQueryFilter(statefulSetList.getItems(), k8sQuery);

        for (V1StatefulSet statefulSet : filteredStatefulSets) {
            statefulSetDtoList.add(toJSON(statefulSet));
        }
        return statefulSetDtoList;
    }

    /**
     * 获取 StatefulSet 详情
     */
    @Override
    public JSONObject getStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1StatefulSet statefulSet = api.readNamespacedStatefulSet(statefulSetName, namespace).execute();
        return toJSON(statefulSet);
    }

    /**
     * 获取 StatefulSet Yaml
     */
    @Override
    public String getStatefulSetYaml(String ip, String namespace, String statefulSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1StatefulSet statefulSet = api.readNamespacedStatefulSet(statefulSetName, namespace).execute();
        return Yaml.dump(statefulSet);
    }

    /**
     * 删除 StatefulSet
     */
    @Override
    public JSONObject deleteStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedStatefulSet(statefulSetName, namespace).execute();
        return toJSON(status);
    }

    /**
     * 重启
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedStatefulSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip              集群节点IP地址
     * @param namespace       命名空间
     * @param statefulSetName StatefulSet 名称
     * @return 重启后的 StatefulSet 对象
     * @throws Exception 当重启失败时抛出
     */
    @Override
    public JSONObject restartStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // strategic-merge-patch a statefulset
        V1Patch patch = buildRestartPatch();
        V1StatefulSet statefulSet = api.patchNamespacedStatefulSet(statefulSetName, namespace, patch).execute();
        return toJSON(statefulSet);
    }

    /**
     * 伸缩
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedStatefulSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip               集群节点IP地址
     * @param namespace        命名空间
     * @param statefulSetName  StatefulSet 名称
     * @param k8sUpdateReplica 副本数更新信息
     * @return 更新后的 StatefulSet 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateStatefulSetReplicas(String ip, String namespace, String statefulSetName, K8sUpdateReplica k8sUpdateReplica) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        AppsV1Api api = new AppsV1Api(apiClient);

        // json-patch a statefulset
        V1Patch v1Patch = buildUpdateReplicaPatch(k8sUpdateReplica);
        V1StatefulSet statefulSet = PatchUtils.patch(
                V1StatefulSet.class,
                () -> api.patchNamespacedStatefulSet(statefulSetName, namespace, v1Patch).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );
        return toJSON(statefulSet);
    }

    /**
     * 调整镜像版本
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedStatefulSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip              集群节点IP地址
     * @param namespace       命名空间
     * @param statefulSetName StatefulSet 名称
     * @param k8sUpdateImage  镜像更新信息
     * @return 更新后的 StatefulSet 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateStatefulSetImages(String ip, String namespace, String statefulSetName, K8sUpdateImage k8sUpdateImage) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        AppsV1Api api = new AppsV1Api(apiClient);

        // json-patch a statefulset
        V1Patch patch = buildUpdateImagePatch(k8sUpdateImage);
//        V1StatefulSet statefulSet = api.patchNamespacedStatefulSet(statefulSetName, namespace, patch).execute();
        V1StatefulSet statefulSet = PatchUtils.patch(
                V1StatefulSet.class,
                () -> api.patchNamespacedStatefulSet(statefulSetName, namespace, patch).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );
        return toJSON(statefulSet);
    }

    /**
     * 修改更新策略
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedStatefulSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip                           集群节点IP地址
     * @param namespace                    命名空间
     * @param statefulSetName              StatefulSet 名称
     * @param k8sUpdateStatefulSetStrategy 更新策略信息
     * @return 更新后的 StatefulSet 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateStatefulSetStrategy(String ip, String namespace, String statefulSetName, K8sUpdateStatefulSetStrategy k8sUpdateStatefulSetStrategy) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        AppsV1Api api = new AppsV1Api(apiClient);

        // json-patch a statefulset
        V1Patch patch = buildUpdateStrategyPatch(k8sUpdateStatefulSetStrategy);
//        V1StatefulSet statefulSet = api.patchNamespacedStatefulSet(statefulSetName, namespace, patch).execute();

        V1StatefulSet statefulSet = PatchUtils.patch(
                V1StatefulSet.class,
                () -> api.patchNamespacedStatefulSet(statefulSetName, namespace, patch).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );
        return toJSON(statefulSet);
    }

}
