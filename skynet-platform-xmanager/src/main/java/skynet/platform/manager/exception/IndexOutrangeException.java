package skynet.platform.manager.exception;

import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;

/**
 * <AUTHOR>
 */
public class IndexOutrangeException extends ApiRequestException {

    private final int min;
    private final int max;

    public IndexOutrangeException(int min, int max) {
        super(ApiRequestErrorCode.DIAGNOSIS_INDEX_OUTRANGE_ERROR);
        this.min = min;
        this.max = max;
    }

    @Override
    public String getMessage() {
        return String.format("%s.[MIN=%s;MAX=%s]", super.getMessage(), min, max);
    }
}
