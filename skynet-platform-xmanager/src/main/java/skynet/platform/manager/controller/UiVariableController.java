package skynet.platform.manager.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.AppContext;
import skynet.platform.manager.k8s.config.K8sConfigProperties;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 所有UI可定制化的变量从此接口获取
 *
 * <AUTHOR>
 * @date 2020/11/9 17:31
 */
@Slf4j
@RestController
@RequestMapping(value = "/skynet", produces = {MediaType.APPLICATION_JSON_VALUE})
public class UiVariableController {

    private final AppContext appContext;
    private final Environment environment;
    private final List<String> pngKeyList = Arrays.asList("skynet.ui.login.png", "skynet.ui.logo.png");

    @Value("${skynet.web-shell.enabled:true}")
    private boolean webShellEnabled;
    @Value("${skynet.web-shell.sftp-enabled:true}")
    private boolean webSftpEnabled;

    private final K8sConfigProperties k8sConfigProperties;

    public UiVariableController(AppContext appContext, Environment environment, K8sConfigProperties k8sConfigProperties) {
        this.appContext = appContext;
        this.environment = environment;
        this.k8sConfigProperties = k8sConfigProperties;
    }

    @GetMapping("/ui/variables")
    public Map<String, Object> getUIVariables() {
        Map<String, Object> ret = new LinkedHashMap<>();
        ret.put("app_title", appContext.getSkynetProperties().getActionTitle());
        for (String key : pngKeyList) {
            String png = environment.getProperty(key);
            if (StringUtils.isNoneBlank(png) && getPngFile(png).exists()) {
                ret.put(key, String.format("./skynet/ui/png?fileName=%s", png));
            }
        }
        ret.put("webshell_enabled", webShellEnabled);
        ret.put("websftp_enabled", webSftpEnabled);
        ret.put("k8s_console_enabled", k8sConfigProperties.getConsole().isEnabled());
        return ret;
    }


    /**
     * 下载指定plugin关联的资源文件
     *
     * @param fileName 相对plugin资源目录的相对路径
     * @return
     * @throws IOException
     */
    @GetMapping(value = "/ui/png")
    public void download(@RequestParam String fileName, HttpServletResponse response) throws IOException {
        log.debug("fileName={} ", fileName);
        File imageFile = getPngFile(fileName);
        if (imageFile.exists()) {
            response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_PNG_VALUE);
            response.getOutputStream().write(FileUtils.readFileToByteArray(imageFile));
            log.debug("download the image ok.[{}]", fileName);
        } else {
            log.error("The image not exist。[{}]", imageFile);
            response.sendError(500, String.format("The image not exist。[%s]", fileName));
        }
        response.flushBuffer();
    }

    private File getPngFile(String fileName) {
        return new File(String.format("%s/doc/%s", appContext.getSkynetHome(), fileName));
    }
}
