package skynet.platform.manager.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.common.domain.SkynetResponse;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.config.SkynetFormAuthProperties;
import skynet.boot.security.form.controller.SkynetSecurityAuthController;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.AccessToken;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.AuthLogin;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.security.SkynetZkUserService;

/**
 * 用户登录管理
 *
 * <AUTHOR> QQ 408365330
 */
@Slf4j
@RestController
@ExposeSwagger2
@Tag(name = "v0.系统登录验证", description = "系统登录验证")//, hidden = true)
public class LoginController implements AuthLogin {

    private final SkynetZkUserService skynetZkUserService;
    private final SkynetSecurityAuthController skynetSecurityAuthController;
    private final SkynetEncryption skynetEncryption;

    public LoginController(AuthenticationManager authenticationManager,
                           SkynetFormAuthProperties authProperties,
                           UserDetailsService userDetailsService,
                           SkynetZkUserService skynetZkUserService,
                           SkynetEncryption skynetEncryption) throws Exception {

        this.skynetSecurityAuthController = new SkynetSecurityAuthController(authenticationManager, authProperties, userDetailsService);
        this.skynetZkUserService = skynetZkUserService;
        this.skynetEncryption = skynetEncryption;
    }

    @Override
    @AuditLog(module = "系统", operation = "用户登录", message = "用户登录=#{#loginInfo.username}")
    public SkynetApiResponse<AccessToken> login(@RequestBody LoginInfo loginInfo) {
        ServletRequestAttributes servlet = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        SkynetSecurityAuthController.SkynetLoginInfo info = new SkynetSecurityAuthController.SkynetLoginInfo();
        info.setUsername(loginInfo.getUsername()).setPassword(loginInfo.getPassword());
        assert servlet != null;
        SkynetResponse<?> obj = skynetSecurityAuthController.login(info, servlet.getRequest(), servlet.getResponse());
        return JSON.parseObject(obj.toJson(), new TypeReference<>() {
        });
    }

    @Override
    @AuditLog(module = "系统", operation = "修改密码", message = "用户=#{#info.username}")
    public SkynetApiResponse<Void> updatePwd(@RequestBody UpdatePasswordInfo info) throws Exception {
        log.debug("updatePwd username={}", info.getUsername());
        try {
            skynetZkUserService.updatePwd(info.getUsername(), skynetEncryption.decrypt(info.getPassword()), skynetEncryption.decrypt(info.getNewpassword()));
            return SkynetApiResponse.success();
        } catch (ApiRequestException e) {
            return SkynetApiResponse.fail(e);
        }
    }

    /**
     * 增加用户
     * <p>
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @param loginInfo
     * @return
     * @throws Exception
     */
    @Override
    public SkynetApiResponse<Void> addUser(@RequestBody LoginInfo loginInfo) throws Exception {
        log.debug("addUser username={}", loginInfo.getUsername());
        try {
            skynetZkUserService.addUser(loginInfo.getUsername(), skynetEncryption.decrypt(loginInfo.getPassword()));
            return SkynetApiResponse.success();
        } catch (ApiRequestException e) {
            return SkynetApiResponse.fail(e);
        }
    }

    /**
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @param loginInfo
     * @return
     * @throws Exception
     */
    @Override
    public SkynetApiResponse<Void> resetPwd(@RequestBody LoginInfo loginInfo) throws Exception {
        try {
            skynetZkUserService.resetPwd(loginInfo.getUsername(), skynetEncryption.decrypt(loginInfo.getPassword()));
            return SkynetApiResponse.success();
        } catch (ApiRequestException e) {
            return SkynetApiResponse.fail(e);
        }
    }

    /**
     * 获取 缺省用户 token ，用于免登陆
     * <p>
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @return
     */
    @Override
    public SkynetApiResponse<AccessToken> token() {
        SkynetResponse<?> obj = skynetSecurityAuthController.token();
        return JSON.parseObject(obj.toJson(), new TypeReference<>() {
        });
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息，包含角色列表
     */
    @GetMapping("/skynet/auth/userinfo")
    @Operation(summary = "获取当前用户信息")
    @ApiOperationSupport(order = 100)
    public SkynetApiResponse<UserInfo> getUserInfo() {
        try {
            // 获取当前认证用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return SkynetApiResponse.fail(ApiRequestErrorCode.USER_AUTH_INVALID);
            }

            String username = authentication.getName();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

            // 提取角色信息，去掉 ROLE_ 前缀
            List<String> roles = authorities.stream()
                    .map(GrantedAuthority::getAuthority)
                    .filter(auth -> auth.startsWith("ROLE_"))
                    .map(auth -> auth.substring(5).toLowerCase()) // 去掉 ROLE_ 前缀并转为小写
                    .collect(Collectors.toList());

            UserInfo userInfo = new UserInfo();
            userInfo.setUsername(username);
            userInfo.setRoles(roles);
            userInfo.setName(username); // 可以根据需要设置显示名称
            userInfo.setIntroduction("User"); // 可以根据角色设置介绍

            return SkynetApiResponse.success(userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return SkynetApiResponse.fail(ApiRequestErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 用户信息响应类
     */
    public static class UserInfo {
        private String username;
        private String name;
        private String introduction;
        private List<String> roles;

        // Getters and Setters
        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIntroduction() {
            return introduction;
        }

        public void setIntroduction(String introduction) {
            this.introduction = introduction;
        }

        public List<String> getRoles() {
            return roles;
        }

        public void setRoles(List<String> roles) {
            this.roles = roles;
        }
    }

    /**************************************************************************************************/

    @GetMapping(PREFIX + "/logout")
    @Operation(summary = "退出")
    @ApiOperationSupport(order = 10)
    public SkynetApiResponse<Void> logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.debug("Logout..");
        SkynetResponse<?> obj = skynetSecurityAuthController.logout(request, response);
        return JSON.parseObject(obj.toJson(), new TypeReference<>() {
        });
    }

    @GetMapping(PREFIX + "/check")
    @Operation(summary = "检查登录")
    @ApiOperationSupport(order = 20)
    public SkynetApiResponse<Void> check() {
        log.debug("check..");
        return SkynetApiResponse.success();
    }

    @GetMapping(PREFIX + "/refresh")
    @Operation(summary = "刷新状态")
    @ApiOperationSupport(order = 10000)
    public SkynetApiResponse<String> refresh(HttpServletRequest request) {
        log.debug("refresh token..");
        SkynetResponse<String> obj = skynetSecurityAuthController.refresh(request);
        return JSON.parseObject(obj.toJson(), new TypeReference<>() {
        });
    }
}
