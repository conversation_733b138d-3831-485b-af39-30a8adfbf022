package skynet.platform.manager.admin.service;

import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.PluginNotExistException;
import skynet.platform.feign.model.PluginDto;

/**
 * <AUTHOR>
 */
public interface V3BaseService {

    NodeDescription checkAction(String plugin, String action);

    PluginDto checkPlugin(String plugin) throws PluginNotExistException;
}
