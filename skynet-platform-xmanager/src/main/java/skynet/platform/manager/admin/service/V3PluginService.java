package skynet.platform.manager.admin.service;

import skynet.platform.feign.model.PluginDto;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface V3PluginService extends V3BaseService {

    void createPlugin(PluginDto pd);

    void savePlugin(PluginDto pd);

    /**
     * reorder plugin
     *
     * @param pluginList plugin code list
     */
    void reorderPlugin(List<String> pluginList);

    void updateProperties(String pluginCode, String value) throws IOException;

    void updateLoggingLevels(String pluginCode, String value) throws IOException;

    String getProperties(String pluginCode) throws IOException;

    String getLoggingLevels(String pluginCode) throws IOException;
}
