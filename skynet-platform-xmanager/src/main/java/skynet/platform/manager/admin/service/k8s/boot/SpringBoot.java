package skynet.platform.manager.admin.service.k8s.boot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import skynet.boot.SkynetProperties;
import skynet.boot.exception.SkynetException;
import skynet.platform.common.domain.BootParam;
import skynet.platform.common.domain.UpdateParam;
import skynet.platform.common.env.BootEnvironmentBuilder;

import java.io.File;
import java.io.FileFilter;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(SpringBoot.BEAN_NAME)
public class SpringBoot extends JavaBoot {

    public static final String BEAN_NAME = "boot.springboot";


    public SpringBoot(SkynetProperties skynetProperties, BootEnvironmentBuilder bootEnvironmentBuilder, Environment environment) {
        super(skynetProperties, bootEnvironmentBuilder, environment);
    }

    @Override
    public List<String> getWorkArgs() throws Exception {
        log.debug("get spring boot work args..");
        long start = System.currentTimeMillis();

        //默认 v2.x
        // springBoot 2.x jar 采用 spring.config.additional-location
        String locationKey = "spring.config.additional-location";

        List<String> lines = super.getWorkArgs();
        // 除-D的所有参数
        for (int index = lines.size() - 1; index >= 0; index--) {
            if (lines.get(index).startsWith("-D")) {
                lines.remove(index);
            }
        }
        lines.add(String.format("--server.port=%d", this.getAppPort()));
        lines.add(String.format("--%s=%s", locationKey, StringUtils.join(getConfigFiles(), ",")));
        log.debug("get spring boot work args, cost={}", System.currentTimeMillis() - start);
        return lines;
    }

    protected List<String> getConfigFiles() {
        return Collections.singletonList(super.getAppConfigFile().toString());
    }

    @Override
    protected List<String> getMain() throws Exception {
        BootParam bootParam = this.getAntActionParam().getBootParam();
        if (bootParam == null || StringUtils.isBlank(bootParam.getMainJar())) {
            throw new SkynetException(-1, "No config main jar.");
        }

        List<String> files = getDependFiles(bootParam);
        List<String> objList = new ArrayList<>();
        objList.addAll(getJavaOpts());
        objList.add("-jar");
        objList.add(files.size() == 1 ? files.getFirst() : bootParam.getMainJar());
        return objList;
    }

    private List<String> getDependFiles(BootParam bootParam) throws URISyntaxException {

        FileFilter fileFilter = new WildcardFileFilter(bootParam.getMainJar(), IOCase.INSENSITIVE);
        List<String> dependFiles = new ArrayList<>();
        for (UpdateParam updateParam : bootParam.getUpdateParams()) {
            dependFiles.add(getFileName(updateParam.getFileUrl()));
        }

        List<String> files = new ArrayList<>();
        for (String s : dependFiles) {
            File f = new File(s);
            if (fileFilter.accept(f))
                files.add(s);
        }
        return files;
    }

    private static String getFileName(String uriString) throws URISyntaxException {

        if (StringUtils.isBlank(uriString) || StringUtils.isBlank(uriString.replace("skynet:", "")) || StringUtils.isBlank(uriString.replace("skynet:null", ""))) {
            log.warn("The uriString invalidity。{}", uriString);
            return null;
        }
        URI uri = new URI(uriString);
        String scheme = uri.getScheme();
        if ("skynet".equals(scheme)) {
            // 将skynet私有协议地址转换为http地址
            return uri.getSchemeSpecificPart();
        }
        return "";
    }
}
