package skynet.platform.manager.security;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.config.SkynetFormAuthAutoConfiguration;
import skynet.boot.security.config.SkynetFormAuthProperties;
import skynet.platform.common.condition.ConditionalOnManager;
import skynet.platform.common.repository.config.IAntConfigService;

/**
 * <AUTHOR>
 * @date 2020/10/16 14:39
 */
@Slf4j
@ConditionalOnManager
@Configuration(proxyBeanMethods = false)
@AutoConfigureBefore(SkynetFormAuthAutoConfiguration.class)
public class SkynetWebSecurityAutoConfiguration {

    @Bean
    public SkynetZkUserService skynetZkUserService(IAntConfigService antConfigService) {
        return new SkynetZkUserService(antConfigService);
    }

    // 覆盖  SkynetFormAuthAutoConfiguration 中的 JwtAuthenticationProvider，有自己的加解密方式
    @Bean
    public DaoAuthenticationProvider skynetZkDaoAuthenticationProvider(SkynetZkUserService skynetZkUserService, SkynetEncryption skynetEncryption) {
        log.info("build SkynetZkDaoAuthenticationProvider.");
        return new SkynetZkDaoAuthenticationProvider(skynetZkUserService, skynetEncryption);
    }

    @Bean
    @ConfigurationProperties(prefix = "skynet.api.auth")
    public AuthProperties authProperties() {
        return new AuthProperties();
    }

    /**
     * 兼容 skynet.api.auth 配置属性，
     *
     * @param authProperties
     * @return
     */
    @Bean
    public BeanPostProcessor skynetFormAuthPropertiesBeanPostProcessor(AuthProperties authProperties) {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
                if (bean instanceof SkynetFormAuthProperties properties) {
                    properties.setFailTriesTimes(authProperties.getFailTriesTimes());
                    properties.setFailLockDurationSecond(authProperties.getFailLockDurationSecond());
                    properties.setJwtExpiresSecond(authProperties.getExpiresSecond());
                }
                return bean;
            }
        };
    }
}