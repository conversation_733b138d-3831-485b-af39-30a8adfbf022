BINARY=skynet-init
VERSION=0.0.1
BUILD=`date +%F`
SHELL := /bin/bash

GO_VERSION=$(shell go env GOVERSION)
#GIT_COMMIT=$(shell git rev-parse HEAD)
#BRANCH=$(shell git rev-parse --abbrev-ref HEAD)
#BUILD_TIME=$(shell date)
GOOS=$(shell go env GOOS)
GOARCH=$(shell go env GOARCH)
#GOBINPATH=$(shell which go)
#GO_PATH=$(abspath ../../../..)

gitTag=$(shell git log --pretty=format:'%h' -n 1)
gitBranch=$(shell git rev-parse --abbrev-ref HEAD)
buildDate=$(shell TZ=Asia/Shanghai date +%FT%T%z)
gitCommit=$(shell git rev-parse --short HEAD)

.PHONY: help all build windows linux darwin clean


versionDir="version"
ldflags="-s -w -X ${versionDir}.version=${VERSION} -X ${versionDir}.gitBranch=${gitBranch} -X '${versionDir}.gitTag=${gitTag}' -X '${versionDir}.gitCommit=${gitCommit}' -X '${versionDir}.buildDate=${buildDate}'"


help:
	@echo "------------------------------------------------------"
	@echo "usage: make <option>"
	@echo "    help   : Show help"
	@echo "    all    : Build multiple binary of this project"
	@echo "    build  : Build the binary of this project for current platform"
	@echo "    windows: Build the windows binary of this project"
	@echo "    linux  : Build the linux binary of this project"
	@echo "    darwin : Build the darwin binary of this project"
	@echo "------------------------------------------------------"
	@echo "Go version: " $(GO_VERSION)
	@echo "Git commit: " $(gitCommit)
	@echo "Branch:     " $(gitBranch)
	@echo "Build time: " $(buildDate)
	@echo "OS/Arch:    " $(GOOS)/$(GOARCH)
	@echo "------------------------------------------------------"

all:  windows linux darwin

build:
	go build -o $(BINARY)-$(GOOS) -ldflags ${ldflags} -o  target/${BINARY}.$(GOOS)

windows:
	@GOOS=windows
	go build -o $(BINARY)-linux -ldflags ${ldflags} -o  target/${BINARY}.windows
linux:
	@GOOS=linux
	go build -o $(BINARY)-linux -ldflags ${ldflags} -o  target/${BINARY}.linux
darwin:
	@GOOS=darwin
	go build -o $(BINARY)-linux -ldflags ${ldflags} -o  target/${BINARY}.darwin

clean:
	rm -f target/$(BINARY).*