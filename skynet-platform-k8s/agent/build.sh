docker rmi alpine:3.17
docker load -i /root/alpine-amd64.tar
docker build -t skynet/agent:amd64 .
docker save skynet/agent:amd64 -o skynet-agent.tar
docker rmi skynet/agent:amd64

docker rmi alpine:3.17
docker load -i /root/alpine-arm64.tar
docker build --platform linux/arm64 --build-arg GOOS=linux --build-arg GOARCH=arm64 -t  skynet/agent:arm64 .
docker save skynet/agent:arm64 -o skynet-agent-arm64.tar
docker rmi skynet/agent:arm64
