package controller

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"iflytek.com/skynet/agent/skynetapp/client"
)

// Reboot worker
func RebootWorker(c *gin.Context) {

	bytes, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		fmt.Println("ReadAll Error: ", err.Error())
		return
	}
	fmt.Println("RebootWorker: ", string(bytes))

	var actionIds []string
	if err := json.Unmarshal(bytes, &actionIds); err != nil {
		fmt.Println("Unmarshal Error: ", err.Error())
		return
	}
	fmt.Println("RebootWorker: ", actionIds)

	for _, actionId := range actionIds {
		fmt.Println("Rebooting: ", actionId)

		replacer := strings.NewReplacer("@", "-", "_", "-")
		aid := replacer.<PERSON>lace(actionId)

		rebootSkynetApp(aid)
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"message": "OK",
	})
}

func rebootSkynetApp(name string) {
	skynetApp, err := client.GetSkynetApp(name)
	if err != nil {
		fmt.Println("GetSkynetApp Error: ", err.Error())
		return
	}
	skynetAppType := getSkynetAppType(skynetApp)
	if skynetAppType == "statefulset" {
		rebootStatefulSet(skynetApp.Name)
	} else if skynetAppType == "daemonset" {
		rebootDaemonSet(skynetApp.Name)
	} else {
		rebootDeployment(skynetApp.Name)
	}
}

func rebootStatefulSet(name string) {

	statefulset, err := client.GetStatefulSet(name)
	if err != nil {
		fmt.Println("GetStatefulSet Error: ", name, err.Error())
		return
	}

	if statefulset.Spec.Template.GetAnnotations() == nil {
		statefulset.Spec.Template.SetAnnotations(map[string]string{})
	}
	annotations := statefulset.Spec.Template.GetAnnotations()
	annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().String()

	_, err = client.UpdateStatefulSet(statefulset)
	if err != nil {
		fmt.Println("UpdateStatefulSet Error: ", name, err.Error())
		return
	}
}

func rebootDaemonSet(name string) {

	daemonset, err := client.GetDaemonSet(name)
	if err != nil {
		fmt.Println("GetDaemonSet Error: ", name, err.Error())
		return
	}

	if daemonset.Spec.Template.GetAnnotations() == nil {
		daemonset.Spec.Template.SetAnnotations(map[string]string{})
	}
	annotations := daemonset.Spec.Template.GetAnnotations()
	annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().String()

	_, err = client.UpdateDaemonSet(daemonset)
	if err != nil {
		fmt.Println("UpdateDaemonSet Error: ", name, err.Error())
		return
	}
}

func rebootDeployment(name string) {

	deployment, err := client.GetDeployment(name)
	if err != nil {
		fmt.Println("GetDeployment Error: ", name, err.Error())
		return
	}

	if deployment.Spec.Template.GetAnnotations() == nil {
		deployment.Spec.Template.SetAnnotations(map[string]string{})
	}
	annotations := deployment.Spec.Template.GetAnnotations()
	annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().String()

	_, err = client.UpdateDeployment(deployment)
	if err != nil {
		fmt.Println("UpdateDeployment Error: ", name, err.Error())
		return
	}
}
