# skynet-platform-k8s 模块

## 模块功能描述

`skynet-platform-k8s` 是 Skynet 平台与 Kubernetes (K8s) 环境进行深度集成和管理的核心模块。它通过一套“Operator + Agent + InitContainer”的模式，将 Skynet 平台的应用生命周期管理能力无缝扩展到 Kubernetes 集群中。这使得用户既可以继续使用 Skynet 的管理界面（`xmanager`）来定义和部署服务，又能充分利用 Kubernetes 的容器编排、弹性和自愈能力。

该模块主要由三个关键子项目组成：

1.  **`operator`**: 一个标准的 Kubernetes Operator，它监听自定义资源 `SkynetApp` (CRD) 的变化，并将其转化为实际的 Kubernetes 原生资源（如 Deployment, Service, ConfigMap 等）。
2.  **`agent`**: 一个运行在 Kubernetes 集群内的轻量级代理，它替代了传统的 `xagent`。它负责与 `xmanager` 通信，监听 ZooKeeper 中的部署指令，并将这些指令转化为 `SkynetApp` CRD 的创建或更新请求。
3.  **`init`**: 一个 `initContainer`，在业务容器启动前运行。它负责从 `xmanager` 拉取服务的详细配置（如配置文件、依赖包），并将其准备到共享的 Volume 中，供主业务容器使用。

通过这三者的协同工作，实现了从 Skynet 平台到 Kubernetes 集群的完整部署和管理链路。

## 内部核心组件/模块

### 1. `operator`

-   **功能**: 遵循 Kubernetes Operator 模式，是 Skynet 应用在 K8s 中的“执行者”。
-   **核心逻辑 (`controllers/skynetapp_controller.go`)**: `Reconcile` 函数是其核心。当一个 `SkynetApp` 资源被创建、更新或删除时，该函数会被触发。
-   **资源解析 (`controllers/skynetapp_parser.go`)**: 负责将 `SkynetApp` CRD 中定义的规范（Spec）解析成一个或多个 Kubernetes 原生资源对象，如 `Deployment`, `StatefulSet`, `Service` 等。
-   **自定义资源定义 (`api/v1alpha1/skynetapp_types.go`)**: 定义了 `SkynetApp` CRD 的数据结构，这是 Operator 与 `k8s-agent` 之间通信的契约。
-   **部署清单 (`config/`)**: 包含了部署 Operator 自身所需的所有 YAML 文件，包括 CRD 定义、RBAC 权限、Manager 的 Deployment 等。

### 2. `agent`

-   **功能**: 作为 `xmanager` 在 Kubernetes 集群内的代理，是 Skynet 指令到 K8s CRD 的“转换器”。
-   **核心逻辑 (`main.go`)**: 监听 ZooKeeper 中特定路径（`/skynet/cluster/topology/{ip}`）的部署指令。
-   **K8s 客户端 (`skynetapp/client`)**: 封装了 `k8s.io/client-go`，提供了与 Kubernetes API Server 交互的能力，主要用于创建和更新 `SkynetApp` 资源。
-   **CRD 构建器 (`skynetapp/builder`)**: `SkynetAppBuilder` 是关键组件，它接收来自 ZooKeeper 的部署信息，并构建出对应的 `SkynetApp` CRD 对象。
-   **兼容 API (`controller` 包)**: 实现了与传统 `xagent` 兼容的 REST API，使得 `xmanager` 无需大幅修改即可管理 K8s 中的节点。

### 3. `init`

-   **功能**: 作为 Pod 中的初始化容器，是业务应用运行前的“环境准备官”。
-   **核心逻辑 (`main.go`)**: 在主容器启动前运行。它会调用 `xmanager` 的 API，获取该服务所需的所有依赖文件和配置文件。
-   **资源下载**: 将获取到的文件（如 `application.properties`、依赖的 `.jar` 或 `.zip` 包）下载并写入到一个 Pod 内的共享 Volume 中。
-   **镜像推送 (`hub_client`)**: 如果依赖的资源是 Docker 镜像（以 `.tar` 格式提供），它还能将其推送到集群内部的镜像仓库中。

## 工作流程

1.  用户在 `xmanager` 界面上将一个服务（如 `my-service@my-plugin`）部署到一个 Kubernetes 节点上。
2.  `xmanager` 将部署信息写入 ZooKeeper。
3.  运行在 K8s 集群中的 `k8s-agent` 监听到 ZooKeeper 的变化。
4.  `k8s-agent` 使用 `SkynetAppBuilder` 将部署信息构建成一个 `SkynetApp` CRD 对象，并通过 K8s API 将其创建或更新到集群中。
5.  `skynet-operator` 监听到 `SkynetApp` CRD 的变化，触发 `Reconcile` 逻辑。
6.  `operator` 的解析器将 `SkynetApp` 的定义转换成一个 `Deployment` 和一个 `Service` 的 YAML 描述。
7.  `operator` 创建这个 `Deployment`。`Deployment` 的 Pod 定义中包含一个 `init` 容器和一个业务容器。
8.  Kubernetes 调度器创建 Pod。首先运行 `init` 容器。
9.  `init` 容器启动，调用 `xmanager` API，下载 `my-service` 的配置文件和依赖包，并放入共享 Volume。
10. `init` 容器执行完毕后，业务容器启动，此时它可以从共享 Volume 中读取到所有必需的配置和文件，正常运行。

## 关键依赖

-   **`k8s.io/client-go`**: 用于 Go 程序与 Kubernetes API Server 进行交互。
-   **`sigs.k8s.io/controller-runtime`**: 构建 Kubernetes Operator 的核心框架。
-   **`github.com/spf13/viper`**: 用于 `k8s-agent` 和 `init` 容器的配置管理。
-   **`github.com/gin-gonic/gin`**: 用于构建 `k8s-agent` 的 Web 服务。
-   **`github.com/go-zookeeper/zk`**: 用于 `k8s-agent` 连接和监听 ZooKeeper。
