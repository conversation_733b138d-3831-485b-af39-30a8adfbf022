### JWT 生成异常解决方案

#### **问题诊断**

在用户登录并生成 JWT (JSON Web Token) 的过程中，系统抛出 `Cannot serialize JWS Payload to JSON` 异常。

根本原因在于，JWT 的 claims（声明）中包含了 Spring Security 的 `GrantedAuthority` 对象。标准的 JSON 序列化工具无法直接处理这种复杂对象，导致序列化失败。

#### **解决方案**

解决方案是在将权限信息放入 JWT 的 claims 之前，先将 `GrantedAuthority` 对象列表转换为一个简单的字符串列表（即角色名称列表）。

---

### **详细步骤**

**第一步：修改 `JwtTokenUtils.java` 文件**

您需要编辑以下文件：
`/Users/<USER>/project/skynet-framework/skynet-boot-project/skynet-boot-context/src/main/java/skynet/boot/security/form/JwtTokenUtils.java`

在该文件的 `generateToken` 方法中，找到以下代码行：

```java
claims.put(AUTHORITIES, authentication.getAuthorities());
```

将其替换为以下代码行：

```java
claims.put(AUTHORITIES, authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()));
```

**修改说明：**
此修改的目的是从 `GrantedAuthority` 对象中提取权限名称（例如 "ROLE_ADMIN", "ROLE_VIEWER"），并将其存储为字符串列表。这样处理后的列表可以被正确地序列化为 JSON 格式。

**第二步：重新构建 `skynet-framework` 项目**

由于您修改了 `skynet-framework` 项目中的文件，必须重新构建该项目以生成更新后的 JAR 包。

1.  打开终端或命令行工具。
2.  进入 `skynet-framework` 项目的根目录：
    ```bash
    cd /Users/<USER>/project/skynet-framework/
    ```
3.  执行 Maven 构建命令：
    ```bash
    mvn clean install
    ```

**第三步：重新构建 `skynet-platform` 项目**

在 `skynet-framework` 这个依赖项目更新后，您需要重新构建主项目 `skynet-platform`，以确保它能加载并使用最新修复的 JAR 包。

1.  返回到 `skynet-platform` 项目的根目录：
    ```bash
    cd /Users/<USER>/project/skynet-platform/
    ```
2.  执行 Maven 构建命令：
    ```bash
    mvn clean install
    ```

完成以上三个步骤后，请重新启动 `skynet-platform` 应用。此时，用户登录时应能成功生成包含角色信息的 JWT，不再出现序列化异常。
