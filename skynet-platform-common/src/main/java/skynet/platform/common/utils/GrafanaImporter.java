package skynet.platform.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;

@Slf4j
public class GrafanaImporter {

    private static final String DATASOURCE_NAME = "Prometheus";
    private static final String DATASOURCE_URL = "http://prometheus.skyos-monitoring.svc.cluster.local:9090";
    private static final Duration TIMEOUT = Duration.ofMinutes(3);
    private static final Duration RETRY_INTERVAL = Duration.ofSeconds(5);

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final String grafanaUrl;
    private final String username;
    private final String password;
    private final File dashboardFile;

    public GrafanaImporter(String grafanaUrl, String username, String password, File dashboardFile) {
        this.grafanaUrl = grafanaUrl;
        this.username = username;
        this.password = password;
        this.dashboardFile = dashboardFile;
        
        // 配置ObjectMapper以避免不必要的转义
        this.objectMapper = new ObjectMapper();
        // 确保JSON序列化时不会转义特殊字符
        this.objectMapper.configure(com.fasterxml.jackson.core.JsonGenerator.Feature.ESCAPE_NON_ASCII, false);
        // 禁用默认的转义功能
        this.objectMapper.configure(com.fasterxml.jackson.core.JsonGenerator.Feature.QUOTE_FIELD_NAMES, true);

        this.restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofSeconds(5))
                .setReadTimeout(Duration.ofSeconds(10))
                .build();

        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return false;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                log.error("Grafana API error: status={}, headers={}",
                        response.getStatusCode(), response.getHeaders());
            }
        });
    }

    public GrafanaImporter(String grafanaUrl, String username, String password) {
        this(grafanaUrl, username, password, null);
    }

    /**
     * 批量导入目录下所有 .json 文件
     */
    public void importAllDashboards(File dashboardDir) {
        if (!dashboardDir.exists() || !dashboardDir.isDirectory()) {
            throw new IllegalArgumentException("Invalid dashboard directory: " + dashboardDir.getAbsolutePath());
        }

        File[] dashboardFiles = dashboardDir.listFiles((dir, name) -> name.endsWith(".json"));
        if (dashboardFiles == null || dashboardFiles.length == 0) {
            log.warn("No JSON dashboard files found in {}", dashboardDir.getAbsolutePath());
            return;
        }

        for (File file : dashboardFiles) {
            try {
                log.info("Importing dashboard from file: {}", file.getName());
                GrafanaImporter importer = new GrafanaImporter(grafanaUrl, username, password, file);
                importer.importDashboard();
            } catch (Exception e) {
                log.error("Failed to import dashboard file: {}", file.getName(), e);
            }
        }
    }

    public void importDashboard() {
        try {
            waitForGrafanaReady();
            String uid = createOrUpdateDatasource();
            JsonNode dashboardJson = loadAndPatchDashboard(uid);
            validateDashboard(dashboardJson);
            pushDashboard(dashboardJson);
            log.info("Dashboard import complete.");
        } catch (Exception e) {
            log.error("Grafana dashboard import failed", e);
            throw new RuntimeException("Grafana dashboard import failed", e);
        }
    }

    private void waitForGrafanaReady() throws InterruptedException {
        String healthUrl = grafanaUrl + "/api/health";
        Instant start = Instant.now();
        while (Duration.between(start, Instant.now()).compareTo(TIMEOUT) < 0) {
            try {
                ResponseEntity<String> response = restTemplate.exchange(
                        healthUrl, HttpMethod.GET, buildAuthEntity(), String.class);
                if (response.getStatusCode().is2xxSuccessful()) {
                    log.info("Grafana is ready.");
                    return;
                }
            } catch (RestClientException e) {
                log.info("Grafana not ready yet, retrying in {}s...", RETRY_INTERVAL.getSeconds());
            }
            Thread.sleep(RETRY_INTERVAL.toMillis());
        }
        throw new IllegalStateException("Timeout waiting for Grafana to be ready.");
    }

    private String createOrUpdateDatasource() throws IOException {
        String queryUrl = grafanaUrl + "/api/datasources/name/" + DATASOURCE_NAME;
        Integer id = null;
        String uid = null;

        try {
            ResponseEntity<String> response = restTemplate.exchange(queryUrl, HttpMethod.GET, buildAuthEntity(), String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode existing = objectMapper.readTree(response.getBody());
                uid = existing.get("uid").asText();
                id = existing.get("id").asInt();
                log.info("Datasource exists. ID={}, UID={}", id, uid);
            }
        } catch (HttpClientErrorException.NotFound ignored) {
            log.info("Datasource not found, will create a new one.");
        }

        ObjectNode ds = objectMapper.createObjectNode();
        ds.put("name", DATASOURCE_NAME);
        ds.put("type", "prometheus");
        ds.put("url", DATASOURCE_URL);
        ds.put("access", "proxy");
        ds.put("isDefault", true);

        // 使用ObjectMapper正确序列化JSON，避免转义
        String datasourceJson = objectMapper.writeValueAsString(ds);
        
        // 记录序列化后的JSON（仅用于调试）
        if (log.isDebugEnabled()) {
            log.debug("Serialized datasource JSON: {}", datasourceJson);
        }
        
        HttpEntity<String> entity = buildAuthEntity(datasourceJson);

        if (id != null) {
            restTemplate.exchange(
                    grafanaUrl + "/api/datasources/" + id,
                    HttpMethod.PUT, entity, String.class);
        } else {
            ResponseEntity<String> createResp = restTemplate.exchange(
                    grafanaUrl + "/api/datasources",
                    HttpMethod.POST, entity, String.class);
            JsonNode created = objectMapper.readTree(createResp.getBody());
            uid = created.path("datasource").path("uid").asText();
        }

        if (uid == null || uid.isEmpty()) {
            throw new IllegalStateException("Failed to obtain datasource UID.");
        }
        return uid;
    }

    private JsonNode loadAndPatchDashboard(String uid) throws IOException {
        JsonNode original = objectMapper.readTree(Files.readAllBytes(dashboardFile.toPath()));

        JsonNode dashboardNode = original.has("dashboard") ? original.get("dashboard") : original;
        patchDatasource(dashboardNode, uid);
        patchTemplatingDatasource(dashboardNode, uid);
        patchAnnotationsDatasource(dashboardNode, uid);

        // 补充字段
        ObjectNode dashboardObj = (ObjectNode) dashboardNode;
        if (!dashboardObj.has("title")) {
            dashboardObj.put("title", "Default Imported Dashboard");
        }
        if (!dashboardObj.has("schemaVersion")) {
            dashboardObj.put("schemaVersion", 26);
        }
        if (!dashboardObj.has("version")) {
            dashboardObj.put("version", 0);
        }

        ObjectNode wrapper = objectMapper.createObjectNode();
        wrapper.put("overwrite", true);
        wrapper.put("folderId", 0);
        wrapper.set("dashboard", dashboardNode);
        return wrapper;
    }

    private void patchDatasource(JsonNode node, String uid) {
        if (node.isObject()) {
            ObjectNode obj = (ObjectNode) node;
            if (obj.has("datasource")) {
                JsonNode ds = obj.get("datasource");
                if (ds.isTextual()) {
                    String dsValue = ds.asText();
                    // 处理 ${DS_PROMETHEUS} 变量占位符
                    if ("${DS_PROMETHEUS}".equals(dsValue) || "prometheus".equalsIgnoreCase(dsValue)) {
                        ObjectNode patched = objectMapper.createObjectNode();
                        patched.put("type", "prometheus");
                        patched.put("uid", uid);
                        obj.set("datasource", patched);
                        log.debug("Patched datasource from '{}' to UID: {}", dsValue, uid);
                    }
                } else if (ds.isObject()) {
                    // 如果datasource已经是对象，检查并更新uid
                    ObjectNode dsObj = (ObjectNode) ds;
                    if (dsObj.has("type") && "prometheus".equalsIgnoreCase(dsObj.get("type").asText())) {
                        dsObj.put("uid", uid);
                        log.debug("Updated existing datasource object UID to: {}", uid);
                    }
                }
            }
            obj.elements().forEachRemaining(child -> patchDatasource(child, uid));
        } else if (node.isArray()) {
            for (JsonNode item : node) {
                patchDatasource(item, uid);
            }
        }
    }

    /**
     * 处理模板变量中的datasource引用
     */
    private void patchTemplatingDatasource(JsonNode node, String uid) {
        if (node.isObject()) {
            ObjectNode obj = (ObjectNode) node;
            if (obj.has("templating") && obj.get("templating").has("list")) {
                JsonNode templatingList = obj.get("templating").get("list");
                if (templatingList.isArray()) {
                    for (JsonNode template : templatingList) {
                        patchDatasourceReference(template, uid, "templating");
                    }
                }
            }
            obj.elements().forEachRemaining(child -> patchTemplatingDatasource(child, uid));
        } else if (node.isArray()) {
            for (JsonNode item : node) {
                patchTemplatingDatasource(item, uid);
            }
        }
    }

    /**
     * 处理annotations中的datasource引用
     */
    private void patchAnnotationsDatasource(JsonNode node, String uid) {
        if (node.isObject()) {
            ObjectNode obj = (ObjectNode) node;
            if (obj.has("annotations") && obj.get("annotations").has("list")) {
                JsonNode annotationsList = obj.get("annotations").get("list");
                if (annotationsList.isArray()) {
                    for (JsonNode annotation : annotationsList) {
                        patchDatasourceReference(annotation, uid, "annotations");
                    }
                }
            }
            obj.elements().forEachRemaining(child -> patchAnnotationsDatasource(child, uid));
        } else if (node.isArray()) {
            for (JsonNode item : node) {
                patchAnnotationsDatasource(item, uid);
            }
        }
    }

    /**
     * 通用的datasource引用处理方法
     */
    private void patchDatasourceReference(JsonNode node, String uid, String context) {
        if (node.has("datasource")) {
            JsonNode ds = node.get("datasource");
            if (ds.isTextual()) {
                String dsValue = ds.asText();
                if ("${DS_PROMETHEUS}".equals(dsValue)) {
                    ObjectNode patched = objectMapper.createObjectNode();
                    patched.put("type", "prometheus");
                    patched.put("uid", uid);
                    ((ObjectNode) node).set("datasource", patched);
                    log.debug("Patched {} datasource from '{}' to UID: {}", context, dsValue, uid);
                }
            }
        }
    }

    private void pushDashboard(JsonNode patchedJson) {
        try {
            // 使用ObjectMapper正确序列化JSON，避免转义
            String dashboardJson = objectMapper.writeValueAsString(patchedJson);
            
            // 验证序列化后的JSON格式
            if (!isValidJson(dashboardJson)) {
                throw new RuntimeException("Serialized JSON is invalid");
            }
            
            // 记录序列化后的JSON（仅用于调试）
            if (log.isDebugEnabled()) {
                log.debug("Serialized dashboard JSON: {}", dashboardJson);
            }
            
            HttpEntity<String> entity = buildAuthEntity(dashboardJson);
            
            ResponseEntity<String> response = restTemplate.exchange(
                    grafanaUrl + "/api/dashboards/db",
                    HttpMethod.POST,
                    entity,
                    String.class
            );
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to import dashboard: " + response.getBody());
            }
            log.info("Dashboard successfully imported.");
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize dashboard JSON", e);
            throw new RuntimeException("Failed to serialize dashboard JSON", e);
        }
    }

    private HttpEntity<String> buildAuthEntity() {
        return buildAuthEntity(null);
    }

    private HttpEntity<String> buildAuthEntity(String body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String basicAuth = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
        headers.set(HttpHeaders.AUTHORIZATION, "Basic " + basicAuth);
        return new HttpEntity<>(body, headers);
    }

    /**
     * 验证JSON字符串格式是否正确
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    private boolean isValidJson(String jsonString) {
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (JsonProcessingException e) {
            log.error("Invalid JSON format: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证dashboard的完整性
     */
    private void validateDashboard(JsonNode dashboardJson) {
        try {
            JsonNode dashboard = dashboardJson.get("dashboard");
            if (dashboard == null) {
                log.warn("Dashboard JSON does not contain 'dashboard' field");
                return;
            }

            // 验证必要字段
            if (!dashboard.has("title")) {
                log.warn("Dashboard missing title field");
            }
            if (!dashboard.has("panels")) {
                log.warn("Dashboard missing panels field");
            }

            // 统计datasource替换情况
            int panelCount = 0;
            int datasourcePatchedCount = 0;
            
            if (dashboard.has("panels") && dashboard.get("panels").isArray()) {
                for (JsonNode panel : dashboard.get("panels")) {
                    panelCount++;
                    if (panel.has("datasource")) {
                        JsonNode ds = panel.get("datasource");
                        if (ds.isObject() && ds.has("uid")) {
                            datasourcePatchedCount++;
                        }
                    }
                }
            }

            log.info("Dashboard validation complete: {} panels, {} datasources patched", 
                    panelCount, datasourcePatchedCount);

        } catch (Exception e) {
            log.warn("Dashboard validation failed: {}", e.getMessage());
        }
    }
}