package skynet.platform.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Slf4j
public class DockerLoginCheckerUtils {

    /**
     * 尝试支持 HTTP 和 HTTPS 两种协议进行 Docker 登录检查
     */
    public static boolean checkDockerLoginWithProtocolSupport(String registryUrl, String username, String password) {
        String httpsUrl = ensureProtocol(registryUrl, "https");

        log.debug("Testing HTTPS login for: {}", httpsUrl);
        if (checkDockerLogin(httpsUrl, username, password)) {
            return true;
        }

        httpsUrl = ensureProtocol(extractDomain(registryUrl), "https");
        log.debug("HTTPS failed, testing HTTP login for: {}}", httpsUrl);

        if (checkDockerLogin(httpsUrl, username, password)) {
            return true;
        }

        String httpUrl = ensureProtocol(registryUrl, "http");
        log.debug("HTTPS failed, testing HTTP login for: {}", httpUrl);
        if (checkDockerLogin(httpUrl, username, password)) {
            return true;
        }
        httpUrl = ensureProtocol(extractDomain(registryUrl), "http");
        log.debug("HTTPS failed, testing HTTP login for: {}", httpUrl);

        return checkDockerLogin(httpUrl, username, password);
    }

    /**
     * 实现 Docker 登录验证
     */
    public static boolean checkDockerLogin(String registryUrl, String username, String password) {
        RestTemplate restTemplate = new RestTemplate();

        // 创建 Basic Auth Header
        String auth = username + ":" + password;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + encodedAuth);

        // 构造请求实体
        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            // 发送 GET 请求到 Docker 私有仓库
            ResponseEntity<String> response = restTemplate.exchange(
                    registryUrl + "/v2/",
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            // 如果返回 200 (HTTP OK)，表示登录成功
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            // 登录失败通常返回 401 (Unauthorized)
//            log.error("Docker login to {} failed: {}", registryUrl, e.getMessage());
            return false;
        }
    }

    public static String extractDomain(String registryUrl) {
        // 找到第一个 "/" 的位置
        int slashIndex = registryUrl.indexOf("/");
        if (slashIndex != -1) {
            // 截取从开始到第一个 "/" 之前的部分
            return registryUrl.substring(0, slashIndex);
        }
        // 如果没有 "/"，直接返回原字符串
        return registryUrl;
    }


    /**
     * 确保 URL 包含指定协议（HTTP 或 HTTPS）
     */
    public static String ensureProtocol(String registryUrl, String protocol) {
        if (!registryUrl.startsWith("http://") && !registryUrl.startsWith("https://")) {
            return protocol + "://" + registryUrl;
        }
        return registryUrl;
    }
}
