//package skynet.platform.common.xray;
//
//import io.micrometer.common.lang.NonNull;
//import io.micrometer.core.instrument.Gauge;
//import io.micrometer.core.instrument.MeterRegistry;
//import io.micrometer.core.instrument.binder.MeterBinder;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service
//public class XrayMeterBinder implements MeterBinder {
//
//    private final List<XrayCollector> xrayCollectorList;
//
//    public XrayMeterBinder(List<XrayCollector> xrayCollectorList) {
//        this.xrayCollectorList = xrayCollectorList;
//    }
//
//
//    @Override
//    public void bindTo(@NonNull MeterRegistry registry) {
//        xrayCollectorList.forEach(collector -> collector.init(registry));
//        XrayStatus xrayStatus = new XrayStatus(xrayCollectorList);
//        xrayStatus.getStatus();
//        Gauge.builder("skynet.xray.collector.status", xrayStatus, XrayStatus::getStatus).strongReference(true).register(registry);
//    }
//
//    static class XrayStatus {
//
//        private final List<XrayCollector> xrayCollectorList;
//
//        public XrayStatus(List<XrayCollector> xrayCollectorList) {
//            this.xrayCollectorList = xrayCollectorList;
//        }
//
//        public int getStatus() {
//            xrayCollectorList.forEach(XrayCollector::collect);
//            return 1;
//        }
//    }
//}
