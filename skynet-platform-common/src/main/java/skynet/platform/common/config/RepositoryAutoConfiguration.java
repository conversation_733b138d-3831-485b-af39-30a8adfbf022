package skynet.platform.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import skynet.boot.SkynetProperties;
import skynet.boot.annotation.EnableSkynetMetrics;
import skynet.boot.annotation.EnableSkynetZookeeper;
import skynet.boot.metrics.SkynetMetricsService;
import skynet.boot.zookeeper.SkynetZkAutoConfiguration;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.impl.AntConfigServiceImpl;
import skynet.platform.common.repository.health.SkynetZkHealthIndicator;


/**
 * <AUTHOR>
 */
@Slf4j
@EnableSkynetMetrics
@EnableSkynetZookeeper
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(SkynetZkAutoConfiguration.class)
public class RepositoryAutoConfiguration {

    @Bean
    public IAntConfigService zookeeperAntConfigService(ZkConfigService zkConfigService, SkynetProperties skynetProperties) throws Exception {
        return new AntConfigServiceImpl(zkConfigService, skynetProperties);
    }

    @Bean
    public OnlineActionManager onlineActionManager(IAntConfigService antConfigService, SkynetMetricsService skynetMetricsService) {
        return new OnlineActionManager(antConfigService, skynetMetricsService);
    }

    @Lazy
    @Bean
    public SkynetZkHealthIndicator skynetZkHealthIndicator(IAntConfigService antConfigService) {
        return new SkynetZkHealthIndicator(antConfigService);
    }
}