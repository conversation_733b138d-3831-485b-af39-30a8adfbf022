package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.Locale;

/**
 * 服务器登录信息
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [Oct 19, 2017 2:17:05 PM]
 */
@Getter
@Setter
public class ServerLoginParam extends Jsonable {

    @JsonProperty(index = 10)
    @JSONField(ordinal = 10)
    private String ip;

    @JsonProperty(index = 20)
    @JSONField(ordinal = 20)
    private int port = 22;

    @JsonProperty(index = 30)
    @JSONField(ordinal = 30)
    private String user = "root";

    @JsonProperty(index = 40)
    @JSONField(ordinal = 40)
    private String pwd;

    @JsonProperty(index = 40)
    @JSONField(ordinal = 45)
    private int timeout = 8;

    @JsonProperty(index = 50)
    @JSONField(ordinal = 50)
    private String desc;

    @JsonProperty(index = 60)
    @JSONField(ordinal = 60)
    private boolean dockerEnabled = true;

    @JsonProperty(index = 61)
    @JSONField(ordinal = 61)
    private boolean monitoringEnabled = true;

    @JsonProperty(index = 62)
    @JSONField(ordinal = 62)
    private boolean schedulerEnabled = true;

    @JsonProperty(index = 70)
    @JSONField(ordinal = 70)
    private String kubeConfig;

    @JsonProperty(index = 71)
    @JSONField(ordinal = 71)
    private String skyosCluster;

    @JsonProperty(index = 72)
    @JSONField(ordinal = 72)
    private String registryUrl;

    @JsonProperty(index = 75)
    @JSONField(ordinal = 75)
    private String registryContextPath;

    @JsonProperty(index = 80)
    @JSONField(ordinal = 80)
    private String registryUsername;

    @JsonProperty(index = 90)
    @JSONField(ordinal = 90)
    private String registryPassword;

    @JsonProperty(index = 100)
    @JSONField(ordinal = 100)
    private String arch;

    @JsonProperty(index = 110)
    @JSONField(ordinal = 110)
    private String acceleratorType = "cpu";

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ServerLoginParam s) {
            return this.ip.trim().equals(s.ip.trim()) && (this.port == s.port) && this.user.trim().equals(s.user.trim()) && this.pwd.trim().equals(s.pwd.trim());
        }
        return super.equals(obj);
    }
    
    public boolean isNvidiaDevicePluginEnabled() {
        return acceleratorType.trim().toLowerCase(Locale.ROOT).equals("gpu");
    }
    
    public boolean isNpuPluginEnabled() {
        return acceleratorType.trim().toLowerCase(Locale.ROOT).equals("npu");
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
