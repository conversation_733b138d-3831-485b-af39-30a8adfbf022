package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

/**
 * 自定义扩展配置项
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExtConfigItem extends Jsonable {

    @JSONField(ordinal = 10)
    private String targetFile;

    @JSONField(ordinal = 20)
    private String encoding;

    @JSONField(ordinal = 30)
    private String text;

    /**
     * 文件权限，如：755， 777
     */
    @JSONField(ordinal = 40)
    private String mode;

    /**
     * 归属用户
     */
    @JSONField(ordinal = 50)
    private String owner;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ExtConfigItem) {
            ExtConfigItem p = (ExtConfigItem) obj;
            return p.toString().equals(this.toString());
        }
        return false;
    }

    @Override
    public int hashCode() {
        return this.toString().hashCode();
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
