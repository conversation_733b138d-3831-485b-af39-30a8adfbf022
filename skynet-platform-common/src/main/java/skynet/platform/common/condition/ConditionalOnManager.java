package skynet.platform.common.condition;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import skynet.boot.security.condition.ConditionalOnSecurity;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@ConditionalOnSecurity
@ConditionalOnProperty(value = "skynet.platform.manager.enabled")
public @interface ConditionalOnManager {

}
