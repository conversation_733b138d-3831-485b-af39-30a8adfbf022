package skynet.platform.common.repository.config.observer;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Observable;
import java.util.Observer;

/**
 * 通用 类型 观察者
 *
 * <AUTHOR>
 */
@Slf4j
public class TObserver<T> implements Observer {

    private final Class<T> clazz;

    public TObserver(Class<T> clazz) {
        this.clazz = clazz;
    }

    public void update(T value) {
    }

    @Override
    public void update(Observable o, Object arg) {

        String strValue = (String) arg;
        log.debug(String.format("setting json value:%s", strValue));
        T setting = null;
        if (StringUtils.isNotBlank(strValue)) {
            try {
                setting = JSON.parseObject(strValue, clazz);
            } catch (Exception e) {
                log.error(String.format("getTSetting is error. type: %s; value json: %s", clazz, strValue), e);
            }
        }
        update(setting);

    }
}
