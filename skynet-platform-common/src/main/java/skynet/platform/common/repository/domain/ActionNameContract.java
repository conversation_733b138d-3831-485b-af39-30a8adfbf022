package skynet.platform.common.repository.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;
import skynet.platform.common.exception.AntException;

/**
 * actionPoint 名称契约
 *
 * <p>
 * {actionCode}@{pluginCode}
 * </p>
 *
 * <AUTHOR>
 */
public class ActionNameContract {

    public ActionNameContract(String point) {
        if (StringUtils.isBlank(point) || point.split("@").length != 2) {
            throw new AntException(String.format("the action point: [%s] is invalid. correct format:{actionCode}@{pluginCode}", point));
        }

        this.point = point.trim();
        this.params = point.trim().split("@");
    }

    public ActionNameContract(String plugin, String action) {
        if (StringUtils.isBlank(plugin) || StringUtils.isBlank(action)) {
            throw new AntException(String.format("the plugin or action not blank.plugin:%s, action:%s", plugin, action));
        }

        this.point = String.format("%s@%s", action, plugin);
        this.params = new String[]{action, plugin};
    }

    /**
     * worker参数: {actionName}@{pluginName}
     * <p>
     * 如：mq-vspp-v1.0@engine
     * </p>
     */
    @JSONField(ordinal = 10)
    private final String point;

    public String getPoint() {
        return point;
    }

    private final String[] params;

    /**
     * 获取Action编码，如：mq-vspp-v1.0
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getActionCode() {
        return params[0];
    }

    /**
     * 获取插件编码 如：ant
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getPluginCode() {
        return params[1];
    }

    /**
     * 获取Action编码，如：mq-vspp-v1.0
     *
     * <pre>
     * getActionCode 代替
     * </pre>
     *
     * @return
     */
    @JSONField(serialize = false)
    @Deprecated
    public String getActionName() {
        return params[0];
    }

    /**
     * 获取插件编码 如：ant
     *
     * <pre>
     * getPluginCode 代替
     * </pre>
     *
     * @return
     */
    @JSONField(serialize = false)
    @Deprecated
    public String getPluginName() {
        return params[1];
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

// private static final Pattern WORKERNAME_PATTERN =
// Pattern.compile("worker\\.(\\d{2,5})");
//
// @Override
// @JSONField(serialize = false)
// public int getPort() {
// Matcher matcher = WORKERNAME_PATTERN.matcher(this.getName());
// if (matcher.find()) {
// return Integer.parseInt(matcher.group(1));
// }
// throw new
// AntException(String.format("the workername: [%s] is invalid. correct format: worker.{port}",
// this.getName()));
// }
