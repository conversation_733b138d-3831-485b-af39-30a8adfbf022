<?xml version="1.0" encoding="UTF-8"?>

<included>
    <appender name="SIFT_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <!--discriminator鉴别器，根据actionId这个key对应的value鉴别日志事件，然后委托给具体appender写日志-->
        <discriminator>
            <key>actionId</key>
            <defaultValue>skynet-action</defaultValue>
        </discriminator>

        <sift>
            <!--直接写日志appender，每一个action创建一个文件-->
            <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <encoder>
                    <pattern>%m%n</pattern>
                </encoder>
                <file>${LOG_PATH}/${actionId}/${actionId}_console.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
                    <fileNamePattern>
                        ${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_PATH}/${actionId}/${actionId}_console.%d{yyyy-MM-dd}.%i.gz}
                    </fileNamePattern>
                    <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-20MB}</maxFileSize>
                    <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-7}</maxHistory>
                    <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
                </rollingPolicy>
            </appender>
        </sift>
    </appender>
    
</included>

