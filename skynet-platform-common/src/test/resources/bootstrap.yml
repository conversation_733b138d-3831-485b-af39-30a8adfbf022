debug: true
encrypt:
    key-store:
        location: skynet-config-server.jks
    keyStore:
        alias: config-server-key
        location: file:/config-server.jks
        password: 453543543
        secret: 43242342
logging:
    config: classpath:logback.xml
    file:
        name: <EMAIL>
        path: ../log
server:
    port: 2230
    servlet:
        session:
            cookie:
                name: SkynetUI
skynet:
    action-point: ant-xmanager@ant
    security:
        base-auth:
            enabled: true
        enabled: true
    zookeeper:
        cluster_name: skynet
        connection_timeout: 30000
        enabled: true
        server_list: 172.31.164.103:2181
        session_timeout: 20000
spring:
    application:
        name: ant-xmanager@ant
