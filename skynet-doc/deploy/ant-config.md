# Skynet Platform 配置文档

本文档详细描述了 Skynet Platform 项目中的所有配置项，按模块分类整理。

## 目录

- [基础配置](#基础配置)
- [服务器配置](#服务器配置)
- [ZooKeeper配置](#zookeeper配置)
- [安全认证配置](#安全认证配置)
- [监控配置](#监控配置)
- [日志配置](#日志配置)
- [Kubernetes配置](#kubernetes配置)
- [性能调优配置](#性能调优配置)

## 基础配置

### Platform 模块配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.action-point` | `ant-xmanager@ant` | 服务坐标标识，格式：服务名@插件名 | 是 |
| `skynet.action-name` | `${skynet.action-point}` | 服务名称，通常引用action-point | 否 |
| `skynet.action-id` | `${skynet.action-point}` | 服务ID，通常引用action-point | 否 |
| `skynet.action-desc` | `${skynet.action-point}` | 服务描述信息 | 否 |
| `skynet.action-code` | `ant-xmanager` | 服务代码标识 | 是 |

### 应用基础配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `server.port` | `2230` (xmanager) / `6230` (xagent) | 服务监听端口 | 否 |
| `server.servlet.session.cookie.name` | `skynet-manager` | Session Cookie名称 | 否 |
| `spring.servlet.multipart.max-file-size` | `4096MB` | 文件上传最大大小 | 否 |
| `spring.servlet.multipart.max-request-size` | `4096MB` | 请求最大大小 | 否 |
| `spring.mvc.pathmatch.matching-strategy` | `ant_path_matcher` | 路径匹配策略 | 否 |

## 服务器配置

### 端口配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.random.port.range.begin` | `22300` | 随机端口范围起始值 | 否 |
| `skynet.random.port.range.end` | `32300` | 随机端口范围结束值 | 否 |
| `skynet.xagent.server.port` | `6230` | Agent服务默认端口 | 否 |

### 系统参数配置

| 配置项名称 | 默认值       | 描述 | 是否必填 |
|-----------|-----------|------|----------|
| `skynet.check-ip.enabled` | `true`    | 是否启用IP检查 | 否 |
| `skynet.auto.deploy.enabled` | `true`    | 是否启用自动部署 | 否 |
| `skynet.xmanager.repo.path` | `../repo` | 仓库路径，用于存储服务包 | 否 |
| `skynet.fetch.server.status.timeout.seconds` | `8`       | 获取服务器状态超时时间(秒) | 否 |
| `skynet.endpoint.route.enabled` | `false`   | 是否启用端点路由 | 否 |

## ZooKeeper配置

### 连接配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.zookeeper.enabled` | `true` | 是否启用ZooKeeper | 是 |
| `skynet.zookeeper.server_list` | `localhost:2181` | ZooKeeper服务器地址列表 | 是 |
| `skynet.zookeeper.cluster_name` | `skynet` | 集群名称 | 否 |

### Spring Cloud ZooKeeper配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `spring.cloud.zookeeper.enabled` | `true` | 是否启用Spring Cloud ZooKeeper | 否 |
| `spring.cloud.zookeeper.discovery.register` | `true` | 是否注册到服务发现 | 否 |
| `spring.cloud.zookeeper.connect-string` | `${skynet.zookeeper.server_list}` | ZooKeeper连接字符串 | 是 |
| `spring.cloud.zookeeper.discovery.root` | `${skynet.zookeeper.cluster_name:skynet}/discovery` | 服务发现根路径 | 否 |
| `spring.cloud.zookeeper.discovery.instance-host` | `${skynet.ip-address}` | 实例主机地址 | 否 |

## 安全认证配置

### 基础认证配置

| 配置项名称 | 默认值                             | 描述 | 是否必填 |
|-----------|---------------------------------|------|----------|
| `skynet.security.sign-auth.enabled` | `true`                          | 是否启用签名认证 | 否 |
| `skynet.security.base-auth.enabled` | `true`                          | 是否启用基础认证 | 否 |
| `skynet.security.base-auth.user.name` | `admin`                         | 基础认证用户名 | 否 |
| `skynet.security.base-auth.user.password` | `sk*******`                     | 基础认证密码 | 否 |
| `skynet.security.sign-auth.app.feign` | `S1K2Y3N4E5T0F8B76E718********` | Feign客户端签名密钥 | 否 |

### 表单认证配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.security.form-auth.enabled` | `true` | 是否启用表单认证 | 否 |
| `skynet.security.form-auth.jwt-expires-second` | `600` | JWT令牌过期时间(秒) | 否 |
| `skynet.security.form-auth.fail-lock-duration-second` | `180` | 登录失败锁定时间(秒) | 否 |
| `skynet.security.form-auth.fail-tries-times` | `5` | 最大登录失败次数 | 否 |
| `skynet.security.form-auth.path-patterns` | `/**` | 表单认证路径模式 | 否 |
| `skynet.security.form-auth.default-login-enabled` | `false` | 是否启用默认登录功能 | 否 |

### RSA密钥配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.security.rsa-private-file` | `classpath:rsa-key/rsa_2048_priv.pem` | RSA私钥文件路径 | 否 |
| `skynet.security.rsa-public-file` | `classpath:rsa-key/rsa_2048_pub.pem` | RSA公钥文件路径 | 否 |

## 监控配置

### Swagger API文档配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.api.swagger2.enabled` | `false` | 是否启用Swagger2 API文档 | 否 |

### Grafana监控配置

| 配置项名称 | 默认值                         | 描述 | 是否必填 |
|-----------|-----------------------------|------|----------|
| `skynet.grafana.actionPoint` | `grafana-server-v6@ant-mon` | Grafana服务坐标 | 否 |
| `skynet.prometheus.actionPoint` | `prometheus@ant-mon`        | Prometheus服务坐标 | 否 |
| `skynet.prometheus.user` | `admin`                     | Prometheus用户名 | 否 |
| `skynet.prometheus.password` | `sk******`                  | Prometheus密码 | 否 |
| `skynet.logging.loki.enabled` | `false`                     | 是否启用Loki日志收集 | 否 |

### 系统监控配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.xray.gpu.query.string` | `index,name,serial,utilization.gpu,utilization.memory,memory.total,memory.free,memory.used,power.draw,power.limit,fan.speed,temperature.gpu,compute_mode,clocks.current.graphics,clocks.current.sm,clocks.current.memory,clocks.current.video,pstate,clocks_throttle_reasons.gpu_idle` | GPU查询字段配置 | 否 |

## 日志配置

### 基础日志配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `logging.file.path` | `../log` | 日志文件路径 | 否 |
| `logging.file.name` | `<EMAIL>` / `<EMAIL>` | 日志文件名称 | 否 |
| `logging.config` | `classpath:logback-xmanager.xml` | 日志配置文件路径 | 否 |

### 日志级别配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `logging.level.com.iflytek` | `INFO` | iflytek包日志级别 | 否 |
| `logging.level.skynet.boot.manager` | `INFO` | Skynet管理器日志级别 | 否 |
| `logging.level.skynet.boot.agent` | `INFO` | Skynet代理日志级别 | 否 |
| `logging.level.skynet.platform.manager.auth.security` | `ERROR` | 安全认证日志级别 | 否 |
| `logging.level.skynet.platform.common.env.BootEnvironment` | `ERROR` | 启动环境日志级别 | 否 |

## Kubernetes配置

### K8s基础配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.k8s.namespace` | `default` | Kubernetes命名空间 | 否 |
| `skynet.k8s.enabled` | `false` | 是否启用Kubernetes支持 | 否 |

## 性能调优配置

### Java虚拟机配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.java.boot.default.javaopts` | `-Xms128M -Xmx2G` | 默认Java启动参数 | 否 |
| `skynet.append.jvm.options.props` | `` | 附加JVM选项属性列表，逗号分隔 | 否 |

### 备份配置

| 配置项名称 | 默认值    | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.backup.enabled` | `true` | 是否启用备份功能 | 否 |

## UI配置

### 界面设置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.ui.title` | `Skynet Manager Center` | UI标题 | 否 |
| `skynet.ui.login.png` | `` | 登录页面图片路径 | 否 |
| `skynet.ui.logo.png` | `` | Logo图片路径 | 否 |

## WebShell配置

| 配置项名称 | 默认值 | 描述 | 是否必填 |
|-----------|--------|------|----------|
| `skynet.web-shell.enabled` | `true` | 是否启用WebShell功能 | 否 |

## 配置示例

### 开发环境配置示例

```properties
# 基础配置
server.port=2230
skynet.action-point=ant-xmanager@ant
skynet.zookeeper.server_list=127.0.0.1:2181
skynet.k8s.namespace=default

# 调试配置
debug=true
skynet.api.swagger2.enabled=true

# 安全配置
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=sk******

# 备份配置
skynet.backup.enabled=false
```

### 生产环境配置示例

```properties
# 基础配置
server.port=2230
skynet.action-point=ant-xmanager@ant
skynet.zookeeper.server_list=zk1:2181,zk2:2181,zk3:2181
skynet.k8s.namespace=production

# 性能配置
skynet.java.boot.default.javaopts=-Xms512M -Xmx4G
skynet.fetch.server.status.timeout.seconds=30

# 安全配置
skynet.security.form-auth.jwt-expires-second=3600
skynet.security.form-auth.fail-lock-duration-second=300

# 监控配置
skynet.grafana.actionPoint=grafana-server@monitoring
skynet.prometheus.actionPoint=prometheus@monitoring

# 备份配置
skynet.backup.enabled=true
```

## 配置验证

在启动应用前，请确保以下关键配置项已正确设置：

1. **ZooKeeper连接** - `skynet.zookeeper.server_list` 必须指向可访问的ZooKeeper集群
2. **服务坐标** - `skynet.action-point` 必须唯一且符合命名规范
3. **端口配置** - 确保配置的端口未被占用
4. **仓库路径** - `skynet.xmanager.repo.path` 目录必须存在且有读写权限
5. **安全配置** - 生产环境必须修改默认密码

## 故障排查

### 常见配置问题

1. **ZooKeeper连接失败**
   - 检查 `skynet.zookeeper.server_list` 配置
   - 确认ZooKeeper服务可访问
   - 检查网络连接和防火墙设置

2. **端口冲突**
   - 修改 `server.port` 配置
   - 检查端口占用情况：`netstat -tlnp | grep 端口号`

3. **权限问题**
   - 检查仓库路径权限
   - 确认日志目录可写
   - 验证临时目录权限

4. **内存不足**
   - 调整 `skynet.java.boot.default.javaopts` 参数
   - 监控JVM内存使用情况
   - 考虑增加物理内存
