package skynet.platform.feign.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import skynet.boot.security.auth.AuthUtils;

import java.util.Map;
import java.util.Objects;

/**
 * * feign请求拦截器 配置，
 * * 在拦截器中添加鉴权的签名信息
 *
 * <AUTHOR>
 */
@Slf4j
public class AuthSignConfig {

    @Bean
    public RequestInterceptor authSignFeignInterceptor(ApiAuthProperties apiAuthProperties) {
        return new AuthSignFeignInterceptor(apiAuthProperties);
    }

    public static class AuthSignFeignInterceptor implements RequestInterceptor {
        private final ApiAuthProperties apiAuthProperties;

        public AuthSignFeignInterceptor(ApiAuthProperties apiAuthProperties) {
            this.apiAuthProperties = apiAuthProperties;
        }

        @Override
        public void apply(RequestTemplate requestTemplate) {
            log.debug("Apply AuthSignFeignInterceptor ...");
            Assert.hasText(apiAuthProperties.getApiKey(), "ant-manager-apiAuthProperties.apiKey is blank.");
            Assert.hasText(apiAuthProperties.getApiSecret(), "ant-manager-apiAuthProperties.apiSecret is blank.");

            log.debug("assembleAuthorizationHeaders header ...");
            Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(
                    Objects.requireNonNull(HttpMethod.valueOf(requestTemplate.method())),
                    requestTemplate.feignTarget().url() + requestTemplate.url(),
                    apiAuthProperties.getApiKey(), apiAuthProperties.getApiSecret(),
                    requestTemplate.bodyTemplate());

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                log.debug("{}={}", entry.getKey(), entry.getValue());
                requestTemplate.header(entry.getKey(), entry.getValue());
            }
            log.debug("Apply AuthSignFeignInterceptor OK.");
        }
    }
}
