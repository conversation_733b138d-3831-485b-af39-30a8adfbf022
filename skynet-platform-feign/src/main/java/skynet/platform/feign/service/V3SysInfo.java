package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.xray.*;

import java.util.List;

/**
 * 主机系统状态信息，调用 统计 集群中每台服务器的信息
 *
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3SysInfo")
@Tag(name = "v3. 服务器状态信息", description = "服务器节点系统状态信息")//, hidden = true)
public interface V3SysInfo {
    String PREFIX = "/skynet/api/v3/sysinfo";

    @GetMapping(value = PREFIX + "/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    ServerSummary getSysSummary() throws Exception;

    @GetMapping(value = PREFIX + "/disk", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<DiskStat[]> getDiskStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;

    @GetMapping(value = PREFIX + "/network", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<NetworkStat[]> getNetworkStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;

    @GetMapping(value = PREFIX + "/gpu", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<GpuStat[]> getGpuStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;

    @GetMapping(value = PREFIX + "/cpu", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<CpuStat> getCpuStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;

    @GetMapping(value = PREFIX + "/mem", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<MemStat> getMemStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;

    @GetMapping(value = PREFIX + "/connection", produces = {MediaType.APPLICATION_JSON_VALUE})
    List<ConnectionStat> getConnectionStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception;
}
