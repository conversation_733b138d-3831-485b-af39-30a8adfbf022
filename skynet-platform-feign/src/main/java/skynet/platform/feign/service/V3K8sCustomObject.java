package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sCustomObject")
@Tag(name = "v3. K8S CustomObject 管理", description = "K8S CustomObject 管理")//, hidden = true)
public interface V3K8sCustomObject {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 列表")

    SkynetApiResponse<List<JSONObject>> getCustomObjects(
            @PathVariable String ip,
            @PathVariable String group,
            @PathVariable String version,
            @PathVariable String plural,
            @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/customobjects/{group}/{version}/{plural}/{customObjectName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 详情")

    SkynetApiResponse<JSONObject> getCustomObject(
            @PathVariable String ip,
            @PathVariable String namespace,
            @PathVariable String group,
            @PathVariable String version,
            @PathVariable String plural,
            @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}/{customObjectName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 详情")

    SkynetApiResponse<JSONObject> getCustomObject(
            @PathVariable String ip,
            @PathVariable String group,
            @PathVariable String version,
            @PathVariable String plural,
            @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/customobjects/{group}/{version}/{plural}/{customObjectName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject Yaml")

    SkynetApiResponse<String> getCustomObjectYaml(
            @PathVariable String ip,
            @PathVariable String namespace,
            @PathVariable String group,
            @PathVariable String version,
            @PathVariable String plural,
            @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}/{customObjectName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject Yaml")

    SkynetApiResponse<String> getCustomObjectYaml(
            @PathVariable String ip,
            @PathVariable String group,
            @PathVariable String version,
            @PathVariable String plural,
            @PathVariable String customObjectName) throws Exception;
}
