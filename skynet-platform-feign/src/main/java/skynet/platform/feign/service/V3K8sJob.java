package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sJob")
@Tag(name = "v3. K8S Job 管理", description = "K8S Job 管理")//, hidden = true)
public interface V3K8sJob {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/jobs", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Job 列表")

    SkynetApiResponse<List<JSONObject>> getJobs(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/jobs/{jobName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Job 详情")

    SkynetApiResponse<JSONObject> getJob(@PathVariable String ip, @PathVariable String namespace, @PathVariable String jobName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/jobs/{jobName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Job Yaml")

    SkynetApiResponse<String> getJobYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String jobName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/jobs/{jobName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 Job")

    SkynetApiResponse<JSONObject> deleteJob(@PathVariable String ip, @PathVariable String namespace, @PathVariable String jobName) throws Exception;
}
