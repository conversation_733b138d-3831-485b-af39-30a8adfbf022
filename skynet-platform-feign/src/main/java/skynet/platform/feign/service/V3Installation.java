package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Installation")
@Tag(name = "v3. Agent管理", description = "Agent增删改查")//, hidden = true)
public interface V3Installation {

    String PREFIX = "/skynet/api/v3/agents";

    @PostMapping(value = PREFIX + "/installation/{ip}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "在指定服务器上安装Agent服务")

    SkynetApiResponse<Void> installAgent(@PathVariable String ip,
                                         @Parameter(description = "是否部署docker") @RequestParam(required = false, defaultValue = "false") boolean dockerEnabled,
                                         @Parameter(description = "是否强制部署") @RequestParam(required = false, defaultValue = "false") boolean isForce);

    @PostMapping(value = PREFIX + "/installation", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "在指定一批服务器上安装Agent")

    SkynetApiResponse<Void> installAgent(@Parameter(description = "是否部署docker") @RequestParam("dockerEnabled") boolean dockerEnabled,
                                         @Parameter(description = "是否强制部署") @RequestParam("isForce") boolean isForce,
                                         @Parameter(description = "ip 地址列表") @RequestBody List<String> ipList);

    @PostMapping(value = PREFIX + "/connection-test", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "连接测试")

    SkynetApiResponse<Void> connectTest(@RequestBody AgentDto dto);
}
