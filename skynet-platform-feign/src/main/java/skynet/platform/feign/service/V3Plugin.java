package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.PluginDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

/**
 * plugin
 *
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Plugin")
@Tag(name = "v3. 应用系统管理", description = "应用系统增删改查")//, hidden = true)
public interface V3Plugin {

    String PREFIX = "/skynet/api/v3/plugins";

    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取所有应用系统")

    SkynetApiResponse<List<PluginDto>> getAllPlugins();

    @GetMapping(value = PREFIX + "/{pluginCode}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取应用系统信息")

    SkynetApiResponse<PluginDto> getPlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode);

    @PostMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "新增应用系统")

    SkynetApiResponse<Void> createPlugin(@RequestBody PluginDto pluginDto) throws Exception;

    @DeleteMapping(value = PREFIX + "/{pluginCode}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除应用系统")

    SkynetApiResponse<Void> deletePlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode);

    /**
     * 更新服务定义 本质上和创建服务定义一样
     *
     * @param pluginCode plugin code
     * @param pluginDto  plugin dto
     * @return void
     * @throws Exception exception
     */
    @PutMapping(value = PREFIX + "/{pluginCode}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新应用系统信息")
    SkynetApiResponse<Void> updatePlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                         @RequestBody PluginDto pluginDto) throws Exception;

    @GetMapping(value = PREFIX + "/{pluginCode}/properties", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取应用系统属性")

    SkynetApiResponse<String> getProperties(@Parameter(description = "系统编码") @PathVariable String pluginCode);

    @PutMapping(value = PREFIX + "/{pluginCode}/properties", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新应用系统属性")

    SkynetApiResponse<Void> updateProperties(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                             @RequestBody(required = false) String body);

    @GetMapping(value = PREFIX + "/{pluginCode}/logging", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取应用系统日志级别配置")

    SkynetApiResponse<String> getLoggingLevels(@Parameter(description = "系统编码") @PathVariable String pluginCode);

    @PutMapping(value = PREFIX + "/{pluginCode}/logging", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新应用系统日志级别配置")

    SkynetApiResponse<Void> updateLoggingLevels(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                                @RequestBody(required = false) String body);
}
