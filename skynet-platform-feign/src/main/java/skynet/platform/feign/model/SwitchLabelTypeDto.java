package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Schema(title = "功能开关类型")
public class SwitchLabelTypeDto extends Jsonable {

    @Schema(title = "功能编码")
    private String code;

    @Schema(title = "显示标题")
    private String title;

    @Schema(title = "输入框提示")
    private String extPlaceHolder;

    @Schema(title = "适用服务类型")
    private List<String> boots = new ArrayList<>(0);

    @Schema(title = "输入框标题")
    private String extTitle;

}
