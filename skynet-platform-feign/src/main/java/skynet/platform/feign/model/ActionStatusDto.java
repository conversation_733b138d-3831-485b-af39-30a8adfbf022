package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(title = "服务状态")
public class ActionStatusDto extends ActionDeploymentDto {

    @Schema(title = "所在服务器ip")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String ip;

    @Schema(title = "agent注册端口")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private int agentPort;

    @Override
    public String toString() {
        return super.toString();
    }
}
