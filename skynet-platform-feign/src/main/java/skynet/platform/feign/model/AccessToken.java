package skynet.platform.feign.model;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * token 信息.
 *
 * <AUTHOR> QQ: 408365330
 */
@Getter
@Setter
public class AccessToken extends Jsonable {

    /**
     * token.
     */
    private String token;

    /**
     * token类型, 默认basic.
     */
    private String tokenHeader = "Bearer";

    public AccessToken() {
    }

    public AccessToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}

//
//    /**
//     * 到期时间， 时间戳，1970年毫秒数.
//     */
//    private long expires;
