<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter-parent</artifactId>
        <version>4.3.4-SNAPSHOT</version>
    </parent>

    <version>${skynet-platform.vision}</version>
    <artifactId>skynet-platform</artifactId>
    <packaging>pom</packaging>
    <name>Skynet Platform Parent</name>
    <description>Skynet Platform Parent</description>

    <properties>
        <skynet-platform.vision>3.4.15</skynet-platform.vision>
        <kubernetes-client-java.version>24.0.0</kubernetes-client-java.version>
        <docker-java.version>3.5.1</docker-java.version>
        <maven.deploy.skip>false</maven.deploy.skip>
        <skipTests>true</skipTests>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <modules>
        <module>skynet-platform-common</module>
        <module>skynet-platform-feign</module>
        <module>skynet-platform-feign-demo</module>
        <module>skynet-platform-xagent</module>
        <module>skynet-platform-xmanager</module>
        <module>skynet-platform-build</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-zookeeper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <!-- kubernetes -->
            <dependency>
                <groupId>io.kubernetes</groupId>
                <artifactId>client-java</artifactId>
                <version>${kubernetes-client-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.kubernetes</groupId>
                <artifactId>client-java-extended</artifactId>
                <version>${kubernetes-client-java.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
