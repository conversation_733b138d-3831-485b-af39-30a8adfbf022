let mockEndpoints = {
  '_links': {
    'self': {
      'href': 'http://172.31.97.147:24046/actuator',
      'templated': false
    },
    'skynet-props': {
      'href': 'http://172.31.97.147:24046/actuator/skynet-props',
      'templated': false
    },
    'skynet-mq-service': {
      'href': 'http://172.31.97.147:24046/actuator/skynet-mq-service',
      'templated': false
    },
    'skynet-nodestatus': {
      'href': 'http://172.31.97.147:24046/actuator/skynet-nodestatus',
      'templated': false
    },
    'skynet-detailstatus': {
      'href': 'http://172.31.97.147:24046/actuator/skynet-detailstatus',
      'templated': false
    },
    'archaius': {
      'href': 'http://172.31.97.147:24046/actuator/archaius',
      'templated': false
    },
    'beans': {
      'href': 'http://172.31.97.147:24046/actuator/beans',
      'templated': false
    },
    'caches': {
      'href': 'http://172.31.97.147:24046/actuator/caches',
      'templated': false
    },
    'caches-cache': {
      'href': 'http://172.31.97.147:24046/actuator/caches/{cache}',
      'templated': true
    },
    'health-path': {
      'href': 'http://172.31.97.147:24046/actuator/health/{*path}',
      'templated': true
    },
    'health': {
      'href': 'http://172.31.97.147:24046/actuator/health',
      'templated': false
    },
    'info': {
      'href': 'http://172.31.97.147:24046/actuator/info',
      'templated': false
    },
    'conditions': {
      'href': 'http://172.31.97.147:24046/actuator/conditions',
      'templated': false
    },
    'configprops': {
      'href': 'http://172.31.97.147:24046/actuator/configprops',
      'templated': false
    },
    'env-toMatch': {
      'href': 'http://172.31.97.147:24046/actuator/env/{toMatch}',
      'templated': true
    },
    'env': {
      'href': 'http://172.31.97.147:24046/actuator/env',
      'templated': false
    },
    'logfile': {
      'href': 'http://172.31.97.147:24046/actuator/logfile',
      'templated': false
    },
    'loggers': {
      'href': 'http://172.31.97.147:24046/actuator/loggers',
      'templated': false
    },
    'loggers-name': {
      'href': 'http://172.31.97.147:24046/actuator/loggers/{name}',
      'templated': true
    },
    'heapdump': {
      'href': 'http://172.31.97.147:24046/actuator/heapdump',
      'templated': false
    },
    'threaddump': {
      'href': 'http://172.31.97.147:24046/actuator/threaddump',
      'templated': false
    },
    'prometheus': {
      'href': 'http://172.31.97.147:24046/actuator/prometheus',
      'templated': false
    },
    'metrics-requiredMetricName': {
      'href': 'http://172.31.97.147:24046/actuator/metrics/{requiredMetricName}',
      'templated': true
    },
    'metrics': {
      'href': 'http://172.31.97.147:24046/actuator/metrics',
      'templated': false
    },
    'scheduledtasks': {
      'href': 'http://172.31.97.147:24046/actuator/scheduledtasks',
      'templated': false
    },
    'mappings': {
      'href': 'http://172.31.97.147:24046/actuator/mappings',
      'templated': false
    },
    'refresh': {
      'href': 'http://172.31.97.147:24046/actuator/refresh',
      'templated': false
    },
    'features': {
      'href': 'http://172.31.97.147:24046/actuator/features',
      'templated': false
    },
    'hystrix.stream': {
      'href': 'http://172.31.97.147:24046/actuator/hystrix.stream',
      'templated': false
    }
  }
}

export default mockEndpoints
