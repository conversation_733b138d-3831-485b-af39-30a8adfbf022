<template>
  <el-container class="backup-container">
    <el-aside width="350px" class="backup-aside">
      <div class="search-box">
        <el-input class="search" v-model="filterName" :placeholder="$t('1016')" clearable />
        <el-tooltip class="item" effect="dark" :content="$t('1017')" placement="top-start">
          <span class="add-btn" @click="createBackup" v-permission="['admin']">
            <i class="el-icon-plus"></i>
          </span>
        </el-tooltip>
      </div>
      <ul class="list-box">
        <li
          v-for="(item, index) of backupListFiltered"
          class="list-item"
          :class="{ active: activeItem.name === item.name }"
          :key="index"
          @click="clickItem(item)"
        >
          <span style="min-height: 20px">{{ index + 1 }}. {{ item.name }}</span>
          <span class="del-btn" @click="deleteBackup(item)" v-permission="['admin']">
            <i class="el-icon-delete"></i>
          </span>
        </li>
      </ul>
    </el-aside>
    <div class="main">
      <el-table :data="definations" class="main-table" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="50" :label="$t('228')" align="center"></el-table-column>
        <el-table-column prop="code" :label="$t('230')" align="center"></el-table-column>
        <el-table-column prop="name" :label="$t('640')" align="center"></el-table-column>
        <el-table-column prop="history" :label="$t('1018')" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.history == null ? '-' : scope.row.history.length }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="current" :label="$t('1019')" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.current == null ? '-' : scope.row.current.length }}</span>
          </template>
        </el-table-column>
        <el-table-column width="160px" :label="$t('239')">
          <template slot-scope="scope">
            <el-link type="primary" @click="showDiff(scope.row)">{{ $t('1020') }}</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false" :destroy-on-close="true" :title="$t('1020')">
      <div class="body">
        <code-diff
          class="code-diff"
          :old-string="selectedRow.history"
          :new-string="selectedRow.current"
          :context="context"
          width="100%"
          outputFormat="side-by-side"
          :useHightlight="useHightlight"
          :isShowNoChange="isShowNoChange"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" icon="el-icon-check" type="primary" @click.stop="onRestore" v-permission="['admin']">{{ $t('272') }}</el-button>
        <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'Backup',
  data() {
    return {
      activeItem: {},
      currentItem: {},
      backupList: [],
      filterName: '',
      definations: [],
      dialogVisible: false,
      context: 9999,
      useHightlight: true,
      isShowNoChange: true,
      selectedRow: ''
    }
  },
  computed: {
    backupListFiltered() {
      if (this.filterName) {
        return this.backupList.filter(x => x.name.includes(this.filterName))
      } else {
        return this.backupList
      }
    }
  },
  created() {
    this.getBackupList()
    this.getCurrentBackup()
  },
  methods: {
    getBackupList() {
      this.$api.backup.getBackups().then(res => {
        this.backupList = res
      })
    },
    getCurrentBackup() {
      this.$api.backup.getBackup('current').then(res => {
        this.currentItem = res
      })
    },
    clickItem(item) {
      this.$api.backup.getBackup(item.name).then(res => {
        this.activeItem = res
        this.definations = this.getDefinationTable(this.activeItem)
      })
    },
    getDefinationTable(activeItem) {
      let defs = []
      if (activeItem.plugins) {
        for (let i = 0; i < activeItem.plugins.length; i++) {
          defs.push({
            code: activeItem.plugins[i].code,
            name: activeItem.plugins[i].name,
            history: activeItem.plugins[i].content,
            current: this.getCurrentDef(activeItem.plugins[i].code)
          })
        }
      }
      if (activeItem.cluster) {
        defs.push({
          code: 'cluster.properties',
          name: locale.t('1021'),
          history: activeItem.cluster.properties,
          current: this.getCurrentDef('cluster.properties')
        })
        defs.push({
          code: 'cluster.loggingLevels',
          name: locale.t('1022'),
          history: activeItem.cluster.loggingLevels,
          current: this.getCurrentDef('cluster.loggingLevels')
        })
      }
      return defs
    },
    getCurrentDef(code) {
      if (code == 'cluster.properties') {
        return this.currentItem.cluster.properties
      } else if (code == 'cluster.loggingLevels') {
        return this.currentItem.cluster.loggingLevels
      } else {
        for (let i = 0; i < this.currentItem.plugins.length; i++) {
          if (this.currentItem.plugins[i].code == code) {
            return this.currentItem.plugins[i].content
          }
        }
        return null
      }
    },
    tableRowClassName({ row, rowIndex }) {
      let history = row.history
      let current = row.current
      // 导出的服务定义中有一行类似于下面这样的注释，需要忽略
      // # Dumped at 2023-09-07 14:10:45: zookeeper[172.31.164.8:2181]
      if (row.code != 'cluster.loggingLevels' && row.code != 'cluster.properties') {
        history = row.history == null ? null : row.history.substring(row.history.indexOf('\n') + 1)
        current = row.current == null ? null : row.current.substring(row.current.indexOf('\n') + 1)
      }
      if (history != current) {
        return 'warning-row'
      }
      return ''
    },
    showDiff(row) {
      this.selectedRow = {
        code: row.code,
        name: row.name,
        history: row.history || '',
        current: row.current || ''
      }
      this.dialogVisible = true
    },
    onRestore() {
      this.$confirm(`${locale.t('1023')}${this.selectedRow.name}[${this.selectedRow.code}${locale.t('1024')}`, locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.$api.backup
          .restoreBackup({
            code: this.selectedRow.code,
            content: this.selectedRow.history
          })
          .then(res => {
            this.$message.success(`${locale.t('1025')}`)
            this.dialogVisible = false
          })
      })
    },
    createBackup() {
      this.$api.backup.createBackup().then(res => {
        this.$message.success(`${locale.t('1026')}`)
        this.getBackupList()
      })
    },
    deleteBackup(item) {
      this.$confirm(`${locale.t('1027')}${item.name}${locale.t('1028')}`, locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.$api.backup.deleteBackup(item.name).then(res => {
          this.$message.success(`${locale.t('1029')}`)
          this.getBackupList()
        })
      })
    }
  }
}
</script>
<style lang="scss">
.backup-container {
  background-color: #ffffff;
  height: 100%;
  padding: 20px;

  .backup-aside {
    height: 100%;
    background: #f5f6fa;
    border: 1px solid #e2e7ee;
    border-radius: 5px;
    padding: 0 0;

    .search-box {
      padding: 16px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .search {
        width: calc(100% - 45px);
      }
      .add-btn {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 32px;
        height: 32px;
        border: 1px solid #e2e7ee;
        border-radius: 3px;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .list-box {
      .list-item {
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        position: relative;
        font-size: 14px;

        .del-btn {
          position: absolute;
          top: 2px;
          right: 6px;
          padding: 10px;
          display: none;
        }
      }
      .list-item.active,
      .list-item:hover {
        border-left: 5px solid #4b72ef;
        background: #eff4fd;
        padding-left: 11px;

        .del-btn {
          display: block;
        }
      }
    }
  }

  .main {
    width: calc(100% - 350px);
    height: 100%;
    padding-left: 16px;

    .main-table {
      height: 100%;
      overflow: auto;
      padding: 24px 16px;
      background: #fff;
      border: 1px solid #e2e7ee;
      border-radius: 5px;

      .warning-row {
        background: oldlace;
      }
    }
  }
}
.d2h-file-side-diff {
  margin-bottom: -5px;
  max-height: 500px;
  overflow: scroll;
}

.d2h-code-side-line {
  padding: 0 0;
}

.d2h-code-side-linenumber {
  padding: 0 0.5rem;
  position: relative;
  line-height: 20px;
  height: 20px;
}
</style>
