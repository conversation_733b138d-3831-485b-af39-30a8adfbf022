<template>
  <div>
    <el-table :data="events" header-row-class-name="headbg" v-loading="loading" :element-loading-text="$t('628')">
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-form label-position="left" inline class="event-table-expand">
            <el-form-item label="Created">
              <span>{{ scope.row.metadata.creationAt }} ( {{ scope.row.metadata.creationAge }})</span>
            </el-form-item>
            <el-form-item label="Name">
              <span>{{ scope.row.metadata.name }}</span>
            </el-form-item>
            <el-form-item label="Namespace">
              <span>{{ scope.row.metadata.namespace }}</span>
            </el-form-item>
            <el-form-item label="Message">
              <span>{{ scope.row.message }}</span>
            </el-form-item>
            <el-form-item label="Reason">
              <span>{{ scope.row.reason }}</span>
            </el-form-item>
            <el-form-item label="Source" v-if="scope.row.source.component">
              <span>{{ scope.row.source.component }}</span>
            </el-form-item>
            <el-form-item label="ReportingComponent" v-if="scope.row.reportingComponent">
              <span>{{ scope.row.reportingComponent }}</span>
            </el-form-item>
            <el-form-item label="ReportingInstance" v-if="scope.row.reportingInstance">
              <span>{{ scope.row.reportingInstance }}</span>
            </el-form-item>
            <el-form-item label="InvolvedObject">
              <span>{{ scope.row.involvedObject.kind }}: {{ scope.row.involvedObject.name }}</span>
            </el-form-item>
            <el-form-item label="Count" v-if="scope.row.count">
              <span>{{ scope.row.count }}</span>
            </el-form-item>
            <el-form-item label="Last seen" v-if="scope.row.lastTimestamp">
              <span>{{ scope.row.lastAge }} ( {{ scope.row.lastAt }})</span>
            </el-form-item>
            <el-form-item label="Type">
              <span>{{ scope.row.type }}</span>
            </el-form-item>
            <!-- TODO: 弹出页面查看JSON --->
            <!-- <el-form-item label="JSON">
              <span>{{ scope.row }} </span>
            </el-form-item> -->
          </el-form>
        </template>
      </el-table-column>
      <el-table-column type="index" width="50" align="center" label="No" />
      <el-table-column prop="type" label="Type" sortable width="80" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="message" label="Message" sortable show-overflow-tooltip> </el-table-column>
      <el-table-column align="center" label="Namespace" width="150" prop="metadata.namespace" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.metadata.namespace }}</span>
        </template>
      </el-table-column>
      <el-table-column label="InvolvedObject" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.involvedObject.kind }}: {{ scope.row.involvedObject.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="count" label="Count" sortable width="100"> </el-table-column>
      <el-table-column label="Age" width="80" prop="metadata.creationAt" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.metadata.creationAt" placement="top">
            <span> {{ scope.row.metadata.creationAge }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Last" width="80" prop="lastAt" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.lastAt" placement="top">
            <span> {{ scope.row.lastAge }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import k8sUtil from '@/utils/k8s/com'
export default {
  data() {
    return {
      events: [],
      loading: false
    }
  },
  props: {
    query: {},
    ip: ''
  },
  watch: {
    query: {
      handler: function(val, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    initData() {
      if (this.ip && this.query) {
        this.loading = true
        this.$api.kubernetes
          .getEvents(this.ip, this.query)
          .then(res => {
            this.events = k8sUtil.parseK8sDataList(res).map(x => {
              x.lastAt = k8sUtil.formatK8sDateTime(x.lastTimestamp)
              x.lastAge = k8sUtil.getK8sDateTimeAge(x.lastTimestamp)
              return x
            })
          })
          .finally(() => {
            this.loading = false
          })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.event-table-expand {
  padding: 0;

  .el-form-item {
    margin: 0;
    width: 80%;
  }

  .el-form-item--mini {
    margin: 0;
  }
}

.el-form-item--mini .el-form-item__label {
  width: 150px !important;
  color: #99a9bf;
}
</style>
