<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onDialogClose"
    :title="$t('668')"
    v-loading="loading"
    :element-loading-text="$t('700')"
  >
    <div class="body">
      <el-form label-width="130px" :model="strategy">
        <el-form-item :label="$t('231')">
          <el-select v-model="strategy.type" :placeholder="$t('701')" style="width: 80%;">
            <el-option :label="$t('702')" value="OnDelete"></el-option>
            <el-option :label="$t('703')" value="RollingUpdate"></el-option>
          </el-select>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content">
              <p>
                <b>{{ $t('704') }}</b>
              </p>
              <p>
                <b>{{ $t('702') }}</b
                >{{ $t('705') }}<br />{{ $t('706') }}<br />{{ $t('707') }}<br />
              </p>
              <p>
                <b>{{ $t('703') }}</b
                >{{ $t('708') }}
              </p>
              <p>{{ $t('709') }}<br />{{ $t('710') }}<br />{{ $t('711') }}<br /></p>
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>

        <el-form-item :label="$t('712')" v-if="strategy.type === 'RollingUpdate'">
          <el-input v-model="strategy.rollingUpdate.maxUnavailable" style="width: 80%;"></el-input>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content">
              <p>
                <b>{{ $t('713') }}</b>
              </p>
              <p>{{ $t('714') }}<br />{{ $t('715') }}<br /></p>
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" icon="el-icon-check" type="primary" @click.stop="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'DaemonsetStrategy',
  components: {},
  props: {
    callback: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      ip: '',
      daemonset: '',
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxUnavailable: '1'
        }
      }
    }
  },
  computed: {},
  methods: {
    open(ip, daemonset) {
      this.ip = ip
      this.daemonset = daemonset
      if (daemonset && daemonset.spec && daemonset.spec.updateStrategy) {
        this.strategy.type = daemonset.spec.updateStrategy.type
        if (daemonset.spec.updateStrategy.rollingUpdate) {
          this.strategy.rollingUpdate.maxSurge = daemonset.spec.updateStrategy.rollingUpdate.maxSurge
          this.strategy.rollingUpdate.maxUnavailable = daemonset.spec.updateStrategy.rollingUpdate.maxUnavailable
        }
      }
      this.dialogVisible = true
    },
    onDialogClose() {
      this.dialogVisible = false
    },
    onConfirm() {
      if (!this.daemonset) {
        this.dialogVisible = false
        return
      }
      this.loading = true
      this.$api.kubernetes
        .daemonsetStrategy(this.ip, this.daemonset.metadata.namespace, this.daemonset.metadata.name, {
          type: this.strategy.type,
          rollingUpdate: this.strategy.type === 'RollingUpdate' ? this.strategy.rollingUpdate : null
        })
        .then(res => {
          this.$message.success(`${locale.t('716')}`)
          this.dialogVisible = false
          if (this.callback) this.callback()
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
