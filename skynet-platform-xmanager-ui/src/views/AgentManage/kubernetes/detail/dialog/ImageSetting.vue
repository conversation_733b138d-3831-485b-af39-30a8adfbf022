<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="1100px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onDialogClose"
    :title="$t('757')"
    v-loading="loading"
    :element-loading-text="$t('758')"
  >
    <div class="body">
      <el-table :data="containers" header-row-class-name="headbg">
        <el-table-column align="center" prop="type" show-overflow-tooltip width="100px" :label="$t('759')" />
        <el-table-column align="left" prop="name" show-overflow-tooltip min-width="120px" :label="$t('640')" />
        <el-table-column align="left" prop="image" show-overflow-tooltip min-width="120px" :label="$t('760')" />
        <el-table-column align="left" prop="version" show-overflow-tooltip min-width="120px" :label="$t('761')" />
        <el-table-column align="left" prop="newVersion" show-overflow-tooltip min-width="120px" :label="$t('762')">
          <template slot-scope="scope">
            <el-input v-model="scope.row.newVersion" :placeholder="$t('763')"></el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" icon="el-icon-check" type="primary" @click.stop="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import { deepCopy } from '@/common/util'
export default {
  name: 'ImageSetting',
  components: {},
  props: {
    callback: {
      type: Function,
      default: null
    },
    kind: {
      type: String,
      // deployment|daemonset|statefulset
      default: 'deployment'
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      ip: '',
      containers: [
        {
          type: locale.t('197'),
          name: 'neginx',
          image: 'imageimage',
          version: 'xx',
          newVersion: 'xxx'
        }
      ]
    }
  },
  computed: {
    // deployment|daemonset|statefulset
    funcName() {
      return `${this.kind}Images`
    }
  },
  methods: {
    open(ip, deployment) {
      let spec = deepCopy(deployment.spec.template.spec)
      let containers = []
      if (spec.initContainers) {
        containers = spec.initContainers.map(x => {
          return this.parseContainer(locale.t('196'), x)
        })
      }
      if (spec.containers) {
        containers = [
          ...containers,
          ...spec.containers.map(x => {
            return this.parseContainer(locale.t('197'), x)
          })
        ]
      }
      this.ip = ip
      this.deployment = deployment
      this.containers = containers
      this.dialogVisible = true
    },
    parseContainer(type, container) {
      container.type = type
      let images = container.image.split(':')
      container.version = images.pop()
      container.image = images.join(':')
      container.newVersion = container.version
      return container
    },
    onDialogClose() {
      this.dialogVisible = false
    },
    onConfirm() {
      let image = {
        initContainers: [],
        containers: []
      }
      let changeSize = 0
      this.containers.forEach(x => {
        if (x.version !== x.newVersion) {
          changeSize++
        }
      })
      if (changeSize === 0) {
        this.$message.warning(locale.t('764'))
        return
      }
      this.containers.forEach(x => {
        (x.type === locale.t('196') ? image.initContainers : image.containers).push({
          image: `${x.image}:${x.newVersion}`
        })
      })
      this.$confirm(`${locale.t('765')}${changeSize}${locale.t('766')}`, locale.t('767'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.loading = true
        this.$api.kubernetes[this.funcName](this.ip, this.deployment.metadata.namespace, this.deployment.metadata.name, image)
          .then(res => {
            this.dialogVisible = false
            if (this.callback) this.callback()
          })
          .finally(() => {
            this.loading = false
          })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.body {
  padding: 0;
  margin: 0;
}
</style>
