<template>
  <el-container class="runtime-container" v-loading="loading" :element-loading-text="loadingText">
    <!--left-->
    <el-aside>
      <el-container style="height: 100%">
        <el-header class="pod-header" :class="{ 'only-title': !isShowSet }">
          <div class="set-part" v-if="isShowSet">
            <div class="item-line">
              <span class="overflow-ellipsis" :title="activeSet.metadata.name">{{ $t('856') }}{{ activeSet.metadata.name }} </span>
              <span class="right"> #{{ activeSet.metadata.annotations['deployment.kubernetes.io/revision'] }} </span>
            </div>
            <div class="status item-line">
              <span
                >{{ $t('857')
                }}<el-popover placement="top" trigger="click">
                  <span>{{ $t('858') }}{{ activeSet.metadata.creationAt }}</span>
                  <el-button type="text" slot="reference"> {{ activeSet.metadata.creationAge }} </el-button>
                </el-popover>
              </span>
              <span>{{ $t('859') }}{{ activeSet.spec.replicas }}</span>
              <span>{{ $t('860') }}{{ activeSet.status.availableReplicas }}</span>
              <span>{{ $t('861') }}{{ activeSet.status.readyReplicas }}</span>
            </div>
            <div class="item-line">
              <span class="right">
                <el-button type="text" @click="onYAML4Replicaset(activeSet)" icon="el-icon-document">YAML </el-button>
                <el-button v-permission="['admin']" type="text" @click="onDelete4Replicaset(activeSet)" icon="el-icon-delete">{{ $t('862') }}</el-button>
              </span>
            </div>
          </div>
          <div>
            <span class="title">{{ $t('863') }}</span>
          </div>
        </el-header>
        <el-main>
          <div class="pod-list">
            <div
              v-for="(item, index) in podDataList"
              :key="index"
              @click="onPodSelected(item.metadata.name, index)"
              :class="['pod-item', podItemActive === index ? 'pod-item-active' : '']"
            >
              <div class="item-line">
                <span class="overflow-ellipsis" :title="item.metadata.name">{{ $t('864') }}{{ item.metadata.name }} </span>
                <span class="right pod-phase" :class="`pod-phase-${item.status.phase}`">{{ item.status.phase }}</span>
              </div>
              <div>
                <span class="left">#{{ index + 1 }}</span>
                <span class="ip-span"> <span class="iph">H</span> {{ item.status.hostIP }}</span>
                <span class="ip-span"><span class="ipc">C</span> {{ item.status.podIP }}</span>
              </div>
              <div class="item-line">
                <span style="font-size: 12px">
                  <span
                    >{{ $t('865')
                    }}<el-popover placement="top" trigger="click">
                      <span>{{ $t('866') }}{{ item.status.startAt }}</span>
                      <el-button type="text" slot="reference"> {{ item.status.startAge }} </el-button>
                    </el-popover>
                  </span>
                </span>
                <span class="right">
                  <el-button type="text" @click.stop="onYAML4Pod(item.metadata.name)" icon="el-icon-document">YAML </el-button>
                  <el-button type="text" @click.stop="onDelete4Pod(item)" icon="el-icon-delete">{{ $t('65') }}</el-button>
                </span>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-aside>

    <!--right-->
    <el-main>
      <el-container style="height: 100%">
        <el-header class="pod-detail-header">
          <span>{{ $t('864') }}</span> <span class="pod-name">{{ activePodPoint.pod }}</span> ：
        </el-header>
        <el-main class="pod-detail-body">
          <!-- 容器详细信息--->
          <pod-detail-pane :param="activePodPoint" />
        </el-main>
      </el-container>
    </el-main>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" :readonly="true" v-if="yamlShowDialog" @close="yamlShowDialog = false" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </el-container>
</template>
<script>
import locale from '@/i18n/index.js'

import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import PodDetailPane from './PodDetailPane'
import k8sUtil from '@/utils/k8s/com'
import qs from 'querystring'
export default {
  name: 'k8sDeploymentDetailTab2',
  components: {
    PodDetailPane,
    YamlDialog,
    ConfirmDialog
  },
  props: {
    target: {
      type: Object,
      default: {
        metadata: {
          annotations: {}
        },
        spec: {},
        status: {}
      }
    },
    // 在Replicaset 时需要显示
    isShowSet: {
      type: Boolean,
      default: false
    },
    labels: {
      type: Object,
      default: {}
    },
    name: {
      type: String,
      default: ''
    },
    kind: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      loadingText: locale.t('628'),
      activePodPoint: {
        pod: ''
      },
      activeSet: {
        metadata: {
          annotations: {}
        },
        spec: {},
        status: {}
      },
      podDataList: [],
      podItemActive: 0,
      //
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  computed: {
    ip() {
      return this.$route.params.ip
    },
    namespace() {
      return this.$route.params.namespace
    }
  },
  watch: {
    // 监听 target 变化，
    target: {
      handler: function(val, oldVal) {
        this.initData(val)
      },
      deep: true
    }
  },
  methods: {
    initData(set) {
      this.activeSet = set
      this.loadingText = locale.t('867')
      this.loading = true
      let query = {
        fieldSelector: `metadata.namespace=${this.namespace}`,
        labelSelector: decodeURIComponent(qs.stringify(this.labels, ',', '='))
      }
      this.$api.kubernetes
        .getPods(this.ip, query)
        .then(res => {
          let pods = k8sUtil.parseK8sDataList(res)
          this.podDataList = pods.filter(p => this.matchOwnerReferences(p.metadata.ownerReferences))
          // TODO: check, 默认选择第一个
          this.onPodSelected(this.podDataList[0].metadata.name, 0)
        })
        .finally(() => {
          this.loading = false
        })
    },
    matchOwnerReferences(ownerReferences) {
      if (ownerReferences && ownerReferences[0]) {
        return ownerReferences[0].kind.toLowerCase() === this.kind && ownerReferences[0].name === this.name
      }
    },
    // 点击卡片传递参数，更新视图
    onPodSelected(name, index) {
      this.podItemActive = index
      this.activePodPoint = {
        ip: this.ip,
        namespace: this.namespace,
        pod: name
      }
    },
    onYAML4Replicaset(set) {
      this.loadingText = `${locale.t('868')}`
      this.loading = true
      this.$api.kubernetes
        .getReplicasetYaml(this.ip, set.metadata.namespace, set.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('727')}${set.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    onDelete4Replicaset(set) {
      let confirmCallback = () => {
        this.loadingText = locale.t('869')
        this.loading = true
        this.$api.kubernetes
          .deleteReplicaset(this.ip, set.metadata.namespace, set.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('870')}${set.metadata.name}${locale.t('682')}`)
            // this.onRefresh()
            // TODO: 关闭 窗口
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('871')}${set.metadata.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onYAML4Pod(pod) {
      this.loadingText = `${locale.t('872')}`
      this.loading = true
      this.$api.kubernetes
        .getPodYaml(this.ip, this.namespace, pod)
        .then(res => {
          this.yamlTitle = `${locale.t('873')}${pod}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    onDelete4Pod(pod) {
      let confirmCallback = () => {
        this.loadingText = locale.t('874')
        this.loading = true
        this.$api.kubernetes
          .deletePod(this.ip, pod.metadata.namespace, pod.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('875')}${pod.metadata.name}${locale.t('682')}`)
            this.initData(this.activeSet)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('876')}${pod.metadata.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    }
  }
}
</script>
<style scoped lang="scss">
.runtime-container {
  $navWidth: 280px;
  $headerHeight: 40px;
  height: 100%;

  .el-button--mini {
    padding: 0;
    margin: 0;
  }

  .el-container {
    height: 100%;
  }

  .el-aside,
  .el-header,
  .el-main {
    padding: 0;
    margin: 1;
    background-color: #fff;
  }

  .el-main {
    border: #f00 0px solid;
    overflow-x: hidden;
  }

  .el-aside {
    width: 310px !important;
    font-size: 14px;
  }

  .el-header {
    height: $headerHeight;
    line-height: $headerHeight;
  }

  .overflow-ellipsis {
    width: 220px;
  }

  .item-line {
    height: 32px;
  }

  .pod-header {
    height: 130px !important;
    padding-right: 10px;
    background-color: #fff !important;

    .set-part {
      border-radius: 5px;
      padding: 0px 10px;
      border: 1px solid #ccc;
      background-color: #f1f4f8;

      .status {
        font-size: 12px;

        > span {
          padding-right: 10px;
        }
      }
    }
  }

  .only-title {
    height: 30px !important;
  }

  .pod-list {
    background-color: #fff;
    height: 90%;
    padding-right: 10px;

    .pod-item {
      display: block;
      height: 90px;
      margin-top: 10px;
      padding: 0px 10px;
      background-color: #e5edf8;

      border-radius: 5px;

      > div {
        line-height: 28px;
        height: 28px;
      }

      .pod-phase {
        // color: #fff;
        // padding: 3px;
        // border-radius: 5px;
        font-size: 12px;
        font-weight: bold;
      }

      .pod-phase-Failed {
        color: #f00;
      }

      .pod-phase-Succeeded {
        color: #ccc;
        color: #505f79;
      }

      .pod-phase-Running {
        color: #367ae0;
      }

      .ip-span {
        float: right;
        padding: 0 10px;
        font-size: 12px;

        .iph {
          border: 1px solid #505f79;
          background-color: #505f79;
          color: #fff;
        }

        .ipc {
          border: 1px solid #234883;
          background-color: #234883;
          color: #fff;
        }
      }

      &.pod-item-active {
        background-color: #367ae0;
        color: #fff;
        position: relative;

        &::after {
          content: '';
          width: 0;
          height: 0;
          border-left: 10px solid #367ae0;
          border-top: 10px solid transparent;
          border-bottom: 10px solid transparent;
          position: absolute;
          right: -10px;
          top: 50%;
          transform: translateY(-50%);
        }

        .el-button--text {
          color: #fff;
          font-weight: bold;
        }

        .pod-phase-Running {
          color: #fff;
        }
      }
    }

    .pod-item:hover {
      cursor: pointer;
    }
  }

  .pod-detail-header {
    height: 39px !important;
    line-height: 39px;
    padding: 0 15px;
    border-bottom: 1px solid $border-color-dark;

    .pod-name {
      color: #367ae0;
      font-weight: bold;
    }
  }

  .pod-detail-body {
    padding: 0 15px;
    border: 0px solid #f00;
  }
}
</style>
