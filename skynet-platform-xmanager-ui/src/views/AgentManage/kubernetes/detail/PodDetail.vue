<template>
  <div class="sky-detail-contanier" v-loading="loading" :element-loading-text="$t('628')">
    <div class="toolbar">
      <div class="left">
        <span>{{ $t('838') }}</span>
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'nodes' } }"> {{ this.ip }} </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'pods' } }">{{ this.namespace }} </el-breadcrumb-item>
          <el-breadcrumb-item>{{ this.pod }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh()">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-document" @click="onYAML()">YAML</el-button>
        <el-button size="mini" type="danger" plain icon="el-icon-delete" @click="onDelete()" v-permission="['admin']">{{ $t('839') }}</el-button>
      </div>
      <div class="clear"></div>
    </div>
    <div class="pods-detail-body">
      <pod-detail-pane :param="param" />
    </div>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="onRefresh" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import PodDetailPane from './PodDetailPane'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import EventBus from '@/components/event/EventBus'
export default {
  name: 'k8sPodDetail',
  components: {
    PodDetailPane,
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      activeTab: 'home',
      loading: false,
      param: {},
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  computed: {
    ip() {
      return this.$route.params.ip
    },
    namespace() {
      return this.$route.params.namespace
    },
    pod() {
      return this.$route.params.pod
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.param = {
        ip: this.ip,
        namespace: this.namespace,
        pod: this.pod
      }
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onYAML() {
      this.loading = true
      this.$api.kubernetes
        .getPodYaml(this.ip, this.namespace, this.pod)
        .then(res => {
          this.yamlTitle = `${locale.t('840')}${this.pod}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    onDelete() {
      let confirmCallback = () => {
        this.loadingText = locale.t('841')
        this.loading = true
        this.$api.kubernetes
          .deletePod(this.ip, this.namespace, this.pod)
          .then(res => {
            this.$message.success(`${locale.t('842')}${this.pod}${locale.t('682')}`)
            // 关闭本页面
            EventBus.$emit('close-tag', this.$route)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('843')}${this.pod}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    }
  }
}
</script>
<style scoped lang="scss">
.pods-detail-body {
  height: calc(100% - 51px);
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 15px;
}
</style>
