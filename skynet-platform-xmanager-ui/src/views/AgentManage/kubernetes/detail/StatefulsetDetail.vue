<template>
  <div class="sky-detail-contanier" v-loading="loading" :element-loading-text="loadingText">
    <div class="toolbar">
      <div class="left">
        <span>{{ $t('877') }}</span>
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'nodes' } }"> {{ this.ip }} </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'statefulsets' } }">{{ this.namespace }} </el-breadcrumb-item>
          <el-breadcrumb-item>{{ this.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-document" @click="onYAML">YAML</el-button>

        <el-button size="mini" type="primary" plain icon="el-icon-monitor" @click="onTerminal">{{ $t('666') }}</el-button>

        <el-button v-permission:disabled="['admin']" size="mini" type="primary" plain icon="el-icon-set-up" @click="onScaling">{{ $t('685') }}</el-button>
        <el-button v-permission:disabled="['admin']" size="mini" type="primary" plain icon="el-icon-s-operation" @click="onHpa">{{ $t('686') }}</el-button>
        &nbsp;&nbsp;
        <el-dropdown v-permission:disabled="['admin']" split-button size="mini" @command="handleCommand($event)" @click="onEditImageVersion()">
          <i class="el-icon-location-information"></i>{{ $t('667')
          }}<el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-odometer" command="strategy">{{ $t('668') }}</el-dropdown-item>
            <el-dropdown-item icon="el-icon-refresh" command="restart">{{ $t('878') }}</el-dropdown-item>
            <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('879') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="clear"></div>
    </div>
    <div class="body statefulset-detail-content">
      <el-tabs v-model="activeTab" tab-position="top">
        <el-tab-pane :label="$t('671')" name="metadata">
          <metadata-detail :target="statefulset" />
        </el-tab-pane>
        <el-tab-pane :label="$t('672')" name="runtime">
          <set-detail :target="statefulset" :labels="labels" :name="name" :kind="'statefulset'" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="yamlRefresh" />

    <replica-scaling ref="scaling" :callback="onRefresh" :kind="'statefulset'" />
    <statefulset-strategy ref="strategy" :callback="onRefresh" />
    <image-setting ref="image" :callback="onRefresh" :kind="'statefulset'" />

    <pod-terminal ref="terminal" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import MetadataDetail from './MetadataDetail'
import SetDetail from './SetDetail'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ReplicaScaling from '@/views/AgentManage/kubernetes/detail/dialog/ReplicaScaling.vue'
import PodTerminal from '@/views/AgentManage/kubernetes/detail/dialog/PodTerminal.vue'
import ImageSetting from '@/views/AgentManage/kubernetes/detail/dialog/ImageSetting.vue'
import StatefulsetStrategy from '@/views/AgentManage/kubernetes/detail/dialog/StatefulsetStrategy.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import EventBus from '@/components/event/EventBus'
import qs from 'querystring'
import k8sUtil from '@/utils/k8s/com'
import pod from '@/utils/k8s/pod'
import { HPA_TEMPLATE } from '@/utils/k8s/yaml'
export default {
  name: 'k8sStatefulsetDetail',
  components: {
    MetadataDetail,
    SetDetail,
    YamlDialog,
    ConfirmDialog,
    ImageSetting,
    ReplicaScaling,
    StatefulsetStrategy,
    PodTerminal
  },
  data() {
    return {
      activeTab: 'runtime',
      loading: false,
      loadingText: locale.t('628'),
      statefulset: {},
      labels: {},
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false,
      yamlRefresh: () => {}
    }
  },
  computed: {
    //   path: ':ip/namespace/:namespace/statefulset/:statefulset',
    ip() {
      return this.$route.params.ip
    },
    namespace() {
      return this.$route.params.namespace
    },
    name() {
      return this.$route.params.statefulset
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loadingText = locale.t('673')
      this.loading = true
      this.$api.kubernetes
        .getStatefulset(this.ip, this.namespace, this.name)
        .then(res => {
          this.statefulset = k8sUtil.parseK8sDataItem(res)
          this.labels = this.statefulset.spec.selector.matchLabels
        })
        .finally(() => {
          this.loading = false
        })
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onYAML() {
      this.loadingText = locale.t('880')
      this.loading = true
      this.$api.kubernetes
        .getStatefulsetYaml(this.ip, this.namespace, this.name)
        .then(res => {
          this.yamlTitle = `${locale.t('881')}${this.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
          this.yamlRefresh = this.onRefresh
        })
        .finally(() => {
          this.loading = false
        })
    },
    onTerminal() {
      let target = this.statefulset
      this.loadingText = locale.t('676')
      this.loading = true
      this.$api.kubernetes
        .getPods(this.ip, {
          labelSelector: decodeURIComponent(qs.stringify(target.spec.selector.matchLabels, ',', '='))
        })
        .then(res => {
          this.$refs.terminal.open(this.ip, target, pod.parsePods(res))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onHpa() {
      this.loadingText = locale.t('692')
      this.loading = true
      this.$api.kubernetes
        .getHorizontalPodAutoscalerYaml(this.ip, this.namespace, this.name, {
          __use_raw_response_data: true // ignore error tips
        })
        .then(res => {
          if (res.data) {
            this.yamlTitle = `${locale.t('693')}${this.name}`
            this.yamlContent = res.data
          } else {
            this.yamlTitle = `${locale.t('694')}${this.name}`
            this.yamlContent = HPA_TEMPLATE.replaceAll('kubernetes-bootcamp', this.name)
              .replaceAll('default', this.namespace)
              .replaceAll('Deployment', 'StatefulSet')
          }
          this.yamlShowDialog = true
          this.yamlRefresh = () => {}
        })
        .finally(() => {
          this.loading = false
        })
    },
    onScaling() {
      this.$refs.scaling.open(this.ip, this.statefulset)
    },
    onEditImageVersion() {
      this.$refs.image.open(this.ip, this.statefulset)
    },
    onRestart() {
      let confirmCallback = () => {
        this.loadingText = locale.t('882')
        this.loading = true
        this.$api.kubernetes
          .statefulsetRestart(this.ip, this.namespace, this.name)
          .then(res => {
            this.$message.success(`${locale.t('883')}${this.name}${locale.t('679')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('884')}${this.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onDeleteDeploy() {
      let confirmCallback = () => {
        this.loadingText = locale.t('885')
        this.loading = true
        this.$api.kubernetes
          .deleteStatefulset(this.ip, this.namespace, this.name)
          .then(res => {
            this.$message.success(`${locale.t('883')}${this.name}${locale.t('682')}`)
            // 关闭 窗口
            EventBus.$emit('close-tag', this.$route)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('886')}${this.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd) {
      if (cmd === 'strategy') {
        this.$refs.strategy.open(this.ip, this.statefulset)
      } else if (cmd === 'restart') {
        this.onRestart()
      } else if (cmd === 'delete') {
        this.onDeleteDeploy()
      }
    }
  }
}
</script>
<style lang="scss">
.statefulset-detail-content {
  height: calc(100% - 51px);

  .el-tabs.el-tabs--top {
    height: 100%;

    .el-tabs__content {
      height: calc(100% - 55px);

      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>
