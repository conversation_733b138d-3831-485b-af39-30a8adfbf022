<template>
  <div class="height-inherit" v-loading="loading" :element-loading-text="$t('628')">
    <div class="region">
      <div class="title">{{ $t('277') }}</div>
      <div class="content">
        <el-row :gutter="24">
          <el-col v-for="(item, index) in baseItems" :lg="{ span: 12, offset: 0 }" :key="index">
            <p class="detail-col">
              <span class="name">{{ item.name }} : </span>
              <span class="value">{{ item.value }}</span>
            </p>
          </el-col>
        </el-row>
        <div class="detail-col">
          <span class="name">{{ $t('834') }}</span>
          <div class="value">
            <span v-for="(val, key) in dataView.metadata.labels" class="meta-label" :key="key">
              <span class="key">{{ key }} </span>
              <el-tooltip :content="val" placement="top-start">
                <span class="val">{{ val.substring(0, 32) }}</span>
              </el-tooltip>
            </span>
          </div>
        </div>
        <div class="detail-col">
          <span class="name">{{ $t('835') }}</span>
          <div class="value">
            <span v-for="(val, key) in dataView.metadata.annotations" class="meta-label" :key="key">
              <span class="key">{{ key }} </span>
              <el-tooltip :content="val" placement="top-start">
                <span class="val">{{ val.substring(0, 32) }}</span>
              </el-tooltip>
            </span>
          </div>
        </div>
        <div class="detail-col">
          <span class="name">Selector: </span>
          <div class="value">
            <span v-for="(val, key) in selector" class="meta-label" :key="key">
              <span class="key">{{ key }} </span>
              <el-tooltip :content="val" placement="top-start">
                <span class="val">{{ val.substring(0, 32) }}</span>
              </el-tooltip>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="region">
      <div class="title">{{ $t('561') }}</div>
      <div class="content">
        <el-table :data="tableData" header-row-class-name="headbg">
          <el-table-column align="center" prop="type" label="type" width="100" show-overflow-tooltip />
          <el-table-column align="center" prop="status" label="status" width="100" show-overflow-tooltip />
          <el-table-column align="left" prop="message" label="message" show-overflow-tooltip />
          <el-table-column align="left" prop="reason" label="reason" show-overflow-tooltip />
          <el-table-column align="center" prop="lastUpdateAge" label="lastUpdate Time" width="150" show-overflow-tooltip />
          <el-table-column align="center" prop="lastTransitionAge" label="lastTransition Time" width="150" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import k8sUtil from '@/utils/k8s/com'
export default {
  name: 'k8sMetadataDetail',
  props: ['target'],
  data() {
    return {
      loading: false,
      dataView: {
        metadata: {
          labels: {},
          annotations: {}
        }
      },
      baseItems: {},
      tableData: [],
      selector: {}
    }
  },
  created() {},
  watch: {
    // 监听 target 变化，
    target: {
      handler: function(val, oldVal) {
        this.initData(val)
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    initData(data) {
      let metadata = data.metadata
      this.baseItems = [
        {
          name: locale.t('836'),
          value: `${metadata.namespace} / ${metadata.name}`
        },
        {
          name: locale.t('837'),
          value: data.kind
        },
        {
          name: locale.t('720'),
          value: `${metadata.creationAt} (${metadata.creationAge})`
        }
      ]
      this.selector = data.spec.selector.matchLabels
      if (data.status && data.status.conditions) {
        this.tableData = data.status.conditions.map(x => {
          x.lastTransitionAt = k8sUtil.formatK8sDateTime(x.lastTransitionTime)
          x.lastTransitionAge = k8sUtil.getDateTimeAge(x.lastTransitionAt)
          x.lastUpdateAt = k8sUtil.formatK8sDateTime(x.lastUpdateTime)
          x.lastUpdateAge = k8sUtil.getDateTimeAge(x.lastUpdateAt)
          return x
        })
      }
      this.dataView = data
    }
  }
}
</script>
