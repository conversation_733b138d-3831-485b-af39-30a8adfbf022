<template>
  <div class="sky-table-contanier endpoint-contanier">
    <div class="toolbar">
      <namespace-select @selectChange="onNamespaceSelectChange" :ip="ip" v-model="namespace" />&nbsp;
      <el-input
        style="width:25%;"
        :placeholder="$t('638')"
        suffix-icon="el-icon-search"
        v-model="filterKeyword"
        @clear="filterKeyword = null"
        clearable
      />
      <span class="right">
        <el-button type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <!-- <el-button type="primary" plain icon="el-icon-plus" @click="onCreate">创建</el-button> -->
        <!-- <el-button type="danger" icon="el-icon-delete" @click="onSelectDelete">{{$t('65')}}</el-button> -->
      </span>
    </div>
    <div class="body">
      <el-table :data="viewList" header-row-class-name="headbg" :height="_setTHeight(120)" v-loading="loading" :element-loading-text="$t('628')">
        <el-table-column align="center" type="index" :label="$t('228')" width="50"> </el-table-column>
        <el-table-column align="left" :label="$t('640')" prop="metadata.name" min-width="120" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link type="primary" @click="onObjectDetail(scope.row)">
              {{ scope.row.metadata.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('641')" width="120" prop="metadata.namespace" sortable show-overflow-tooltip />
        <el-table-column align="left" :label="$t('569')">
          <template slot-scope="scope">
            <span v-for="(subset, i) in scope.row.subsets" :key="i">
              <el-tag v-for="(address, j) in subset.addresses" :key="j">
                {{ address.ip }}
              </el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('233')">
          <template slot-scope="scope">
            <span v-for="(subset, i) in scope.row.subsets" :key="i">
              <el-tag v-for="(port, j) in subset.ports" :key="j"> {{ port.port }} | {{ port.protocol }} </el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('644')" width="100" prop="metadata.creationAt" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
              <span> {{ scope.row.metadata.creationAge }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('239')" width="120">
          <template slot-scope="scope">
            <el-dropdown split-button plain size="mini" icon="el-icon-view" @command="handleCommand($event, scope.row)" @click="onYAML(scope.row)">
              <span>{{ $t('645') }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-permission="['admin']" icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="onRefresh" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import NamespaceSelect from '@/views/AgentManage/kubernetes/components/namespace-select/index.vue'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import k8sUtil from '@/utils/k8s/com'
export default {
  name: 'Endpoints',
  components: {
    NamespaceSelect,
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      namespace: '',
      filterKeyword: null,
      dataList: [],
      viewList: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    }
  },
  watch: {
    filterKeyword: {
      handler: function(val, oldVal) {
        this.filterList(val)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.kubernetes
        .getEndpoints(this.ip)
        .then(res => {
          this.dataList = k8sUtil.parseK8sDataList(res)
          this.filterList(this.filterKeyword)
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterList(filterKeyword) {
      var namespace = this.namespace
      this.viewList = filterKeyword ? this.dataList.filter(item => item.metadata.name.indexOf(filterKeyword) >= 0) : this.dataList
      this.viewList = namespace ? this.viewList.filter(item => item.metadata.namespace === namespace) : this.viewList
    },
    onNamespaceSelectChange(val) {
      this.namespace = val
      this.filterList(this.filterKeyword)
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onSelectDelete() {
      // TODO:
    },
    onObjectDetail(row) {
      // TODO:
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getEndpointYaml(this.ip, row.metadata.namespace, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('908')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    onDelete(row) {
      let confirmCallback = () => {
        this.loadingText = locale.t('909')
        this.loading = true
        this.$api.kubernetes
          .deleteEndpoint(this.ip, row.metadata.namespace, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('910')}${row.metadata.name}${locale.t('650')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('911')}${row.metadata.name} ] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd, row) {
      if (cmd === 'delete') {
        this.onDelete(row)
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/styles/variables.scss';

.endpoint-manage-contanier {
  .el-tag {
    margin-right: 2px;
  }
}
</style>
