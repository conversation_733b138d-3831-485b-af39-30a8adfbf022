<template>
  <div class="sky-detail-contanier" v-loading="loading" :element-loading-text="$t('628')">
    <div class="toolbar">
      <div class="left">
        <span>{{ $t('921') }}</span>
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'nodes' } }"> {{ this.ip }} </el-breadcrumb-item>
          <el-breadcrumb-item>{{ this.nodename }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh()">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-document" @click="onYAML()">{{ $t('922') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-s-shop" @click="onTerminal">{{ $t('769') }}</el-button>
        <el-button v-permission="['admin']" v-if="nodeData.spec.unschedulable" size="mini" type="primary" plain icon="el-icon-video-play" @click="onUncordon">{{
          $t('923')
        }}</el-button>
        <el-button v-permission="['admin']" v-else size="mini" type="danger" plain icon="el-icon-video-pause" @click="onCordon">{{ $t('924') }}</el-button>
        <el-button v-permission="['admin']" size="mini" type="danger" plain icon="el-icon-delete" @click="onDrain">{{ $t('925') }}</el-button>
      </div>
      <div class="clear"></div>
    </div>

    <div class="body node-detail-body">
      <el-tabs v-model="activeTab" tab-position="top">
        <el-tab-pane :label="$t('926')" name="NodeInfo">
          <NodeInfo :ip="ip" :nodename="nodename" :nodeData="nodeData" />
        </el-tab-pane>
        <el-tab-pane :label="$t('927')" name="NodeStatus">
          <NodeStatus :ip="ip" :nodename="nodename" :status="nodeData.status" :metrics="nodeMetrics.usage" />
        </el-tab-pane>
        <el-tab-pane :label="$t('890')" name="NodePods">
          <NodePods ref="nodePods" :ip="ip" :nodename="nodename" />
        </el-tab-pane>
        <el-tab-pane :label="$t('760')" name="NodeImages">
          <NodeImages :tableData="nodeData.status.images" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="onRefresh" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import NodeInfo from './NodeInfo'
import NodeStatus from './NodeStatus'
import NodePods from './NodePods'
import NodeImages from './NodeImages'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'kubernetesNodeDetail',
  components: {
    NodeInfo,
    NodeStatus,
    NodePods,
    NodeImages,
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      activeTab: 'NodeInfo',
      loading: false,
      nodeData: {
        spec: {
          unschedulable: false
        },
        status: {
          conditions: [],
          images: []
        }
      },
      nodeMetrics: {
        usage: {}
      },
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  computed: {
    ...mapGetters(['skynetInfo', 'uiVars']),
    k8sConsoleEnabled() {
      return this.uiVars.k8s_console_enabled
    },
    ip() {
      return this.$route.params.ip
    },
    nodename() {
      return this.$route.params.nodename
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 防止刷新页面导致params.nodeObj为空，重新接口请求一下
      this.loading = true
      this.$api.kubernetes
        .getNode(this.ip, this.nodename)
        .then(res => {
          this.nodeData = res
        })
        .finally(() => {
          this.loading = false
        })
      this.$refs.nodePods.initData()
      this.$api.kubernetes.getNodeMetric(this.ip, this.nodename).then(res => {
        this.nodeMetrics = res
      })
    },
    onTerminal() {
      //  path: ':ip/node/:node/console'
      window.open(`/#/kubernetes/${this.ip}/node/${this.nodename}/console`)
    },
    onRefresh() {
      this.initData()
      // this.configHeight()
    },
    onCordon() {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .cordonNode(this.ip, this.nodename)
          .then(res => {
            this.$message.success(`${locale.t('928')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('929')}`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onUncordon() {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .uncordonNode(this.ip, this.nodename)
          .then(res => {
            this.$message.success(`${locale.t('930')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('931')}`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onDrain() {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .drainNode(this.ip, this.nodename)
          .then(res => {
            this.$message.success(`${locale.t('932')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('933')}`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onYAML() {
      this.loading = true
      this.$api.kubernetes
        .getNodeYaml(this.ip, this.nodename)
        .then(res => {
          this.yamlTitle = `${locale.t('934')}${this.nodename}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss">
.sky-detail-contanier {
  .node-detail-body {
    height: calc(100% - 51px);

    .el-tabs.el-tabs--top {
      height: 100%;

      .el-tabs__content {
        height: calc(100% - 55px);
        overflow-y: auto;
      }
    }
  }
}
</style>
