<template>
  <div class="sky-table-contanier daemonsets-contanier" v-loading="loading" :element-loading-text="loadingText">
    <div class="toolbar">
      <namespace-select @selectChange="onNamespaceSelectChange" :ip="ip" v-model="namespace" />&nbsp;
      <el-input
        style="width:25%;"
        :placeholder="$t('638')"
        suffix-icon="el-icon-search"
        v-model="filterKeyword"
        @clear="filterKeyword = null"
        clearable
      />
      <span class="right">
        <el-button type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button v-permission="['admin']" type="primary" plain icon="el-icon-plus" @click="onCreate">{{ $t('639') }}</el-button>
      </span>
    </div>
    <div class="body">
      <el-table :data="viewList" header-row-class-name="headbg" :height="_setTHeight(120)">
        <el-table-column align="center" type="index" :label="$t('228')" width="50"> </el-table-column>
        <el-table-column align="left" :label="$t('640')" prop="metadata.name" min-width="120" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link type="primary" @click="onObjectDetail(scope.row)">
              {{ scope.row.metadata.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('641')" width="120" prop="metadata.namespace" sortable show-overflow-tooltip />
        <el-table-column align="center" label="Desired">
          <template slot-scope="scope">
            <span>{{ scope.row.status.desiredNumberScheduled }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Current">
          <template slot-scope="scope">
            <span>{{ scope.row.status.currentNumberScheduled }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Ready">
          <template slot-scope="scope">
            <span>{{ scope.row.status.numberReady }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Up to Date">
          <template slot-scope="scope">
            <span>{{ scope.row.status.updatedNumberScheduled }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Available">
          <template slot-scope="scope">
            <span>{{ scope.row.status.numberAvailable }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('644')" width="100" prop="metadata.creationAt" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
              <span> {{ scope.row.metadata.creationAge }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('239')" width="120">
          <template slot-scope="scope">
            <el-dropdown split-button plain size="mini" icon="el-icon-view" @command="handleCommand($event, scope.row)" @click="onYAML(scope.row)">
              <span>{{ $t('645') }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="el-icon-monitor" command="terminal">{{ $t('666') }}</el-dropdown-item>
                <el-dropdown-item v-permission="['admin']" icon="el-icon-refresh" command="restart">{{ $t('442') }}</el-dropdown-item>
                <el-dropdown-item v-permission="['admin']" icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <yaml-dialog
      :ip="ip"
      :value="yamlContent"
      :title="yamlTitle"
      :create="yamlCreate"
      v-if="yamlShowDialog"
      @close="yamlShowDialog = false"
      @refresh="onRefresh"
    />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
    <pod-terminal ref="terminal" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import NamespaceSelect from '@/views/AgentManage/kubernetes/components/namespace-select/index.vue'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import PodTerminal from '@/views/AgentManage/kubernetes/detail/dialog/PodTerminal.vue'
import qs from 'querystring'
import k8sUtil from '@/utils/k8s/com'
import pod from '@/utils/k8s/pod'
import { DAEMONSET_TEMPLATE } from '@/utils/k8s/yaml'
export default {
  name: 'Daemonsets',
  components: {
    NamespaceSelect,
    YamlDialog,
    ConfirmDialog,
    PodTerminal
  },
  data() {
    return {
      loading: false,
      loadingText: locale.t('628'),
      namespace: '',
      filterKeyword: null,
      dataList: [],
      viewList: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false,
      yamlCreate: false
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    }
  },
  watch: {
    filterKeyword: {
      handler: function(val, oldVal) {
        this.filterList(val)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loadingText = locale.t('628')
      this.loading = true
      this.$api.kubernetes
        .getDaemonsets(this.ip)
        .then(res => {
          this.dataList = k8sUtil.parseK8sDataList(res)
          this.filterList(this.filterKeyword)
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterList(filterKeyword) {
      var namespace = this.namespace
      this.viewList = filterKeyword ? this.dataList.filter(item => item.metadata.name.indexOf(filterKeyword) >= 0) : this.dataList
      this.viewList = namespace ? this.viewList.filter(item => item.metadata.namespace === namespace) : this.viewList
    },
    onNamespaceSelectChange(val) {
      this.namespace = val
      this.filterList(this.filterKeyword)
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onSelectDelete() {
      // TODO:
    },
    onObjectDetail(row) {
      this.$router.push({
        name: 'k8sDaemonsetDetail',
        params: {
          nodeIp: this.ip,
          namespace: row.metadata.namespace,
          daemonset: row.metadata.name
        }
      })
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getDaemonsetYaml(this.ip, row.metadata.namespace, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('987')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
          this.yamlCreate = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    onCreate() {
      this.yamlTitle = `${locale.t('647')}`
      this.yamlContent = DAEMONSET_TEMPLATE
      this.yamlShowDialog = true
      this.yamlCreate = true
    },
    formatTime(dateTime) {
      return k8sUtil.formatK8sDateTime(dateTime)
    },
    onDelete(row) {
      let confirmCallback = () => {
        this.loadingText = locale.t('681')
        this.loading = true
        this.$api.kubernetes
          .deleteDaemonset(this.ip, row.metadata.namespace, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('988')}${row.metadata.name}${locale.t('650')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('989')}${row.metadata.name} ] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd, row) {
      if (cmd === 'yaml') {
        this.onYAML(row)
      } else if (cmd === 'restart') {
        this.onRestart(row)
      } else if (cmd === 'delete') {
        this.onDelete(row)
      } else if (cmd === 'terminal') {
        this.onTerminal(row)
      }
    },
    onTerminal(row) {
      this.loading = true
      this.$api.kubernetes
        .getPods(this.ip, {
          labelSelector: decodeURIComponent(qs.stringify(row.spec.selector.matchLabels, ',', '='))
        })
        .then(res => {
          this.$refs.terminal.open(this.ip, row, pod.parsePods(res))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onRestart(row) {
      let confirmCallback = () => {
        this.loadingText = locale.t('677')
        this.loading = true
        this.$api.kubernetes
          .daemonsetRestart(this.ip, row.metadata.namespace, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('678')}${row.metadata.name}${locale.t('990')}`)
            this.onRefresh()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('991')}${row.metadata.name} ] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    }
  }
}
</script>
<style scoped lang="scss">
.daemonset-contanier {
}
</style>
