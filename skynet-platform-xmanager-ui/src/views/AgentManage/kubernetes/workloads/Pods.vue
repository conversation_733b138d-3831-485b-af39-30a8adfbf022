<template>
  <div class="sky-table-contanier pods-contanier" v-loading="loading" :element-loading-text="loadingText">
    <div class="toolbar">
      <namespace-select @selectChange="onNamespaceSelectChange" :ip="ip" v-model="namespace" />
      &nbsp;
      <el-input
        style="width:25%"
        :placeholder="$t('638')"
        suffix-icon="el-icon-search"
        v-model="filterKeyword"
        @clear="filterKeyword = null"
        clearable
      />
      <span class="right">
        <el-button type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button v-permission:disabled="['admin']" type="primary" plain icon="el-icon-plus" @click="onCreate">{{ $t('639') }}</el-button>
      </span>
    </div>
    <div class="body">
      <el-table :data="viewList" header-row-class-name="headbg" :height="_setTHeight(120)" :row-class-name="deletionRowClassName">
        <el-table-column type="index" width="50" align="center" :label="$t('228')" />
        <el-table-column :label="$t('640')" prop="metadata.name" min-width="150" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link type="primary" @click="onObjectDetail(scope.row)">
              {{ scope.row.metadata.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('641')" width="120" prop="metadata.namespace" sortable show-overflow-tooltip />
        <el-table-column :label="$t('477')" width="160" prop="status.phase" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-for="(item, index) in scope.row.status.conditions" :key="index">
              <el-popover placement="top-start" trigger="hover">
                <div class="status-process">
                  <div>
                    <i :class="['iconfont', item.ok ? (item.status ? ' icon-success-fill' : 'icon-success-fill offline') : 'icon-reeor-fill']"></i>
                    <span> {{ item.name }} </span> <span>({{ item.lastTransitionAt }})</span>
                  </div>
                  <div v-if="!item.status">{{ $t('845') }}{{ item.message }}</div>
                  <div v-if="!item.status">{{ $t('846') }}{{ item.reason }}</div>
                </div>
                <span slot="reference" style="cursor: pointer">
                  <i :class="['iconfont', item.ok ? (item.status ? ' icon-success-fill' : 'icon-success-fill offline') : 'icon-reeor-fill']"></i>
                </span>
              </el-popover>
            </span>
            <span>{{ scope.row.status.phase }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('947')" width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-for="(item, index) in scope.row.containers" :key="index">
              <el-popover placement="top-start" trigger="hover">
                <div class="status-process">
                  <div>
                    <i :class="['iconfont', item.ready ? 'icon-success-fill' : 'icon-reeor-fill', `pod-${item.state.type}`]"></i>
                    <span> {{ item.state.type }} </span>
                    <span
                      ><b>{{ item.name }}</b> [{{ item.group }}]
                    </span>
                  </div>
                  <div>
                    <span> {{ item.state.reason }} </span>
                  </div>
                  <div>
                    <span> {{ item.state.message }} </span>
                  </div>
                  <div v-if="item.state.startedAt">
                    <span>{{ $t('948') }}{{ item.state.startedAt }} </span>
                  </div>
                  <div>
                    <span>{{ $t('949') }}{{ item.image }} </span>
                  </div>
                  <div v-if="item.state.exitCode != null">
                    <span>{{ $t('950') }}{{ item.state.exitCode }} </span>
                  </div>
                </div>
                <span slot="reference" style="cursor: pointer">
                  <i :class="['iconfont', item.ready ? 'icon-success-fill' : 'icon-reeor-fill', `pod-${item.state.type}`]"></i>
                </span>
              </el-popover>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="restartCount" width="100" :label="$t('1007')" align="center" sortable show-overflow-tooltip />
        <el-table-column prop="spec.nodeName" :label="$t('853')" min-width="120" sortable show-overflow-tooltip />
        <!-- <el-table-column label="容器组IP" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <p v-for="(item, index) in scope.row.status.podIPs" :key="index">{{ item.ip }}</p>
        </template>
      </el-table-column> -->
        <el-table-column align="center" :label="$t('644')" width="100" prop="metadata.creationAt" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
              <span> {{ scope.row.metadata.creationAge }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('239')" width="120">
          <template slot-scope="scope">
            <el-dropdown split-button plain size="mini" icon="el-icon-view" @command="handleCommand($event, scope.row)" @click="onYAML(scope.row)">
              <span>{{ $t('645') }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-permission:disabled="['admin']" icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <yaml-dialog
      :ip="ip"
      :value="yamlContent"
      :title="yamlTitle"
      :create="yamlCreate"
      v-if="yamlShowDialog"
      @close="yamlShowDialog = false"
      @refresh="onRefresh"
    />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import NamespaceSelect from '@/views/AgentManage/kubernetes/components/namespace-select/index.vue'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import pod from '@/utils/k8s/pod'
import { POD_TEMPLATE } from '@/utils/k8s/yaml'
export default {
  name: 'pods',
  components: {
    NamespaceSelect,
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      loadingText: locale.t('628'),
      namespace: '',
      filterKeyword: null,
      dataList: [],
      viewList: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false,
      yamlCreate: false
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    }
  },
  watch: {
    filterKeyword: {
      handler: function(val, oldVal) {
        this.filterList(val)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loadingText = locale.t('628')
      this.loading = true
      this.$api.kubernetes
        .getPods(this.ip, {})
        .then(res => {
          this.dataList = pod.parsePods(res)
          this.filterList(this.filterKeyword)
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterList(filterKeyword) {
      var namespace = this.namespace
      this.viewList = filterKeyword ? this.dataList.filter(item => item.metadata.name.indexOf(filterKeyword) >= 0) : this.dataList
      this.viewList = namespace ? this.viewList.filter(item => item.metadata.namespace === namespace) : this.viewList
    },
    deletionRowClassName({ row, rowIndex }) {
      return row.metadata.deletionTimestamp ? 'deletion-row' : ''
    },
    onNamespaceSelectChange(val) {
      this.namespace = val
      this.filterList(this.filterKeyword)
    },
    // 刷新按钮
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onObjectDetail(row) {
      this.$router.push({
        name: 'k8sPodDetail',
        params: {
          ip: this.ip,
          namespace: row.metadata.namespace,
          pod: row.metadata.name
        }
      })
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getPodYaml(this.ip, row.metadata.namespace, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('873')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
          this.yamlCreate = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    onCreate() {
      this.yamlTitle = `${locale.t('647')}`
      this.yamlContent = POD_TEMPLATE
      this.yamlShowDialog = true
      this.yamlCreate = true
    },
    onDelete(row) {
      let confirmCallback = () => {
        this.loadingText = locale.t('841')
        this.loading = true
        this.$api.kubernetes
          .deletePod(this.ip, row.metadata.namespace, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('1008')}${row.metadata.name}${locale.t('650')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('1009')}${row.metadata.name} ] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd, row) {
      if (cmd === 'delete') {
        this.onDelete(row)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.pods-contanier {
  .status-process {
    p {
      width: 280px;
      display: block;
      justify-content: space-between;
    }
  }
}
</style>
