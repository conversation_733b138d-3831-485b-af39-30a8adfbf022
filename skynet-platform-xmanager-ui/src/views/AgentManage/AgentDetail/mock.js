import locale from '@/i18n/index.js'
let overview = {
  actionPoint: 'ant-xagent@ant',
  ip: '*************',
  port: 2230,
  sshUser: 'root',
  sshPort: 22,
  description: 'This is a description',
  serverTags: [locale.t('545'), 'GPU'],
  hardware: locale.t('546'),
  version: '2.2.0',
  status: 'ONLINE',
  pid: 123123,
  startupTime: '2020-07-20 12:23:22',
  runningDuration: locale.t('547'),
  springbootEndpoints: [
    {
      name: 'env',
      link: 'http://localhost:6230/actuator/env'
    },
    {
      name: 'health',
      link: 'http://localhost:6230/actuator/health'
    }
  ]
}
export { overview }
