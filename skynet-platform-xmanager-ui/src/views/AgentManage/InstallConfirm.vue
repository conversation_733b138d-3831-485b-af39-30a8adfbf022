<template>
  <confirm-dialog ref="confirmDialogRef" :isDragVerify="false">
    <div slot="append" class="append">
      <div>
        <el-checkbox v-model="isForce">{{ $t('622') }}</el-checkbox>
      </div>
      <div>
        <el-checkbox v-if="agentType==='server'" v-model="isInstallDocker">{{ $t('623') }}</el-checkbox>
      </div>
    </div>
  </confirm-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
export default {
  name: 'InstallConfirm',
  components: {
    ConfirmDialog
  },
  data() {
    return {
      isForce: false,
      isInstallDocker: false,
      agentType: ''
    }
  },
  methods: {
    open(agentType, operateNames, confirmCallback) {
      this.isForce = false
      this.isInstallDocker = false
      this.agentType = agentType
      const thisVue = this
      console.log('operateNames', operateNames)
      let cb = () => {
        confirmCallback({
          isInstallDocker: thisVue.isInstallDocker,
          isForce: thisVue.isForce
        })
      }
      this.$refs.confirmDialogRef.open(locale.t('624'), operateNames, cb)
    }
  }
}
</script>
<style scoped lang="scss">
.append {
  margin-top: 20px;
  & > div {
    margin-top: 8px;
  }
}
</style>
