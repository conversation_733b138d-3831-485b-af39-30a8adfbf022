import locale from '@/i18n/index.js'
export default {
  actionCode: '',
  pluginCode: '',
  actionPoint: '',
  actionName: '',
  description: '',
  index: 0,
  port: 0,
  homePageURL: '',
  referencedFiles: [],
  startupConfig: {
    workingDir: '',
    runnableJar: '',
    cmd: 'start.sh',
    javaCmdOptions: '',
    skynetRunParam: '',
    programArguments: '',
    sysEnvironments: {},
    dockerRunOptions: '',
    dockerRunCmdAndArgs: '',
    dockerContainerName: '',
    signalToStop: 15,
    logFile: ''
  },
  healthCheckConfig: {
    type: 'pid',
    url: '',
    delaySeconds: 0,
    intervalSeconds: 0,
    timeoutSeconds: 0,
    retryTimes: 0
  },
  integrationConfig: {
    logbackLogCollection: false,
    meshEnabled: false
  },
  switchLabels: [
    {
      code: '',
      name: '',
      value: true,
      extProperty: ''
    }
  ],
  tags: [],
  properties: '',
  loggingLevels: '',
  __view_type: 'BaseBoot',
  __view_protocol: 'HTTP',
  __view_sys_envs: '',
  __view_functions: [],
  __view_ref_files: []
}
