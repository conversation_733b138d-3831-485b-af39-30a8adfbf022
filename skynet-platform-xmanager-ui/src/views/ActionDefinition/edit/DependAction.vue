<template>
  <div class="depend-action">
    <div v-if="edit" class="buttons">
      <el-button size="mini" type="primary" plain icon="el-icon-set-up" @click="onConfig">{{ $t('345') }}</el-button>
    </div>
    <div>
      <prompt class="prompt-wrap">{{ $t('346') }}</prompt>
    </div>

    <section-container bgColor="inherit" :isCollapsable="false" :title="$t('347')" class="config-block">
      <div v-for="(item, index) in runtimeList" :key="item.code" class="depend-item">
        <span> {{ index + 1 }} . {{ item.name }} [ {{ item.code }} ] </span>
      </div>
      <div v-if="runtimeList.length === 0" class="text">
        <span>{{ $t('348') }}</span>
      </div>
    </section-container>
    <section-container bgColor="inherit" :isCollapsable="false" :title="$t('349')" class="config-block">
      <div v-for="(item, index) in callingList" :key="item.code" class="depend-item">
        <span> {{ index + 1 }} . {{ item.name }} [ {{ item.code }} ] </span>
      </div>
      <div v-if="callingList.length === 0" class="text">{{ $t('350') }}</div>
    </section-container>

    <DependActionTransfer ref="DependActionTransferRef"></DependActionTransfer>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import SectionContainer from '@/components/Container/SectionContainer'
import Prompt from '@/components/common/Prompt'
import DependActionTransfer from './DependActionTransfer'
export default {
  name: 'DependAction',
  components: {
    SectionContainer,
    Prompt,
    DependActionTransfer
  },
  props: {
    basicConfigData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    edit: {
      type: Boolean,
      default: true
    },
    dependActions: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      allList: [],
      runtimeList: [],
      callingList: []
    }
  },
  watch: {
    dependActions(value) {
      if (!value) {
        return
      }
      console.log('dependActions......')
      // 通过  dependActions 获取 runtimeList 和 callingList 列表，呈现到页面
      // 根据code 获取 nodedesc
      // value 数据格式：[{ code: 'skynet-lb-dashboard@ant', type: 'runtime' }, { code: 'tlb-a-v1426@ant',  type: 'calling' }]
      let actionPointList = value.map(x => x.code)
      this.loading = true
      this.$api.definition
        .fetchActionDescList(actionPointList)
        .then(list => {
          console.log(list)
          // 根据 dependActions 原始数据 按照type 分类，
          let allList = list.map(item => {
            const y = value.find(x => x.code === item.code)
            if (y) {
              item.type = y.type
            }
            return item
          })
          this.initList(allList)
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
  mounted() {},
  methods: {
    initList(allList) {
      // allList =[{ code: 'skynet-lb-dashboard@ant',   type: 'runtime' }, { code: 'tlb-a-v1426@ant',  type: 'calling' }]
      // 主动排除自己
      let currentActionPoint = `${this.basicConfigData.actionCode}@${this.basicConfigData.pluginCode}`
      allList = allList.filter(item => item.code !== currentActionPoint)
      this.runtimeList = allList.filter(item => item.type === 'runtime')
      this.callingList = allList.filter(item => item.type === 'calling')
      this.allList = allList
    },
    // 功能：
    // 1. 显示：根据 依赖actions code 获取 对应的服务名称和服务坐标，在页面上显示；
    // 2. 排序：显示的 列表，在编辑状态下能 修改 顺序；
    // 3. 配置：弹出对话框，选择 服务定义中所有的 服务列表，排除当前的服务  (basicConfigData.pluginCode)
    onConfig() {
      let currentActionPoint = `${this.basicConfigData.actionCode}@${this.basicConfigData.pluginCode}`
      this.$refs.DependActionTransferRef.open(this.allList, currentActionPoint, dependActions => {
        // todo: 暂时设置测试数据
        // dependActions = [{ code: 'skynet-lb-dashboard@ant', type: 'runtime' }, { code: 'tlb-a-v1426@ant', type: 'calling' }, { code: 'tlb-b-v1426@ant', type: 'calling' }]
        // TODO: 弹框回调出来的数据
        console.log('dependActions====>', dependActions)
        // 传递给父组件（作用：保存时候调用接口进行传参）【sync语法糖简写】

        dependActions = dependActions.filter(item => item.code !== currentActionPoint)
        this.$emit('update:dependActions', dependActions)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.buttons {
  height: 43px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid $border-color-light;
}

.buttons,
.tmpl-propmt {
  margin-bottom: 15px;
}

.depend-action {
  padding: 0 20px 0 0;

  .text,
  .depend-item {
    width: calc(100% - 80px);
    // min-height: 90px;
    margin-left: 13px;
    border: 1px dashed $border-color-dark;
    padding: 5px 15px;
    color: #909399;

    .line {
      height: 25px;
      line-height: 25px;
    }
  }
  .depend-item {
    color: #505f79;
    margin-top: 10px;
  }

  .no-data {
    color: $border-color-dark;
  }

  .prompt-wrap {
    margin-bottom: 10px;
  }
}

.config-block {
  margin-top: 20px !important;
}
</style>
