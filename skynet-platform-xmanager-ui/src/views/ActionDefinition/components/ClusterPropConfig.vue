<template>
  <kv-config
    :title="showTitle ? $t('257') : ''"
    :showOperations="showOperations"
    :isCollapsable="isCollapsable"
    :initialCollapsed="initialCollapsed"
    :refreshFunc="refresh"
    :submitFunc="submit"
    ref="kvConfigRef"
  ></kv-config>
</template>
<script>
import locale from '@/i18n/index.js'

import KVConfig from './KVConfig'
export default {
  name: 'ClusterPropConfig',
  components: {
    'kv-config': KVConfig
  },
  props: {
    showTitle: {
      type: Boolean,
      default: true
    },
    showOperations: {
      type: Boolean,
      default: true
    },
    isCollapsable: {
      type: Boolean,
      default: true
    },
    initialCollapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.$refs.kvConfigRef.refresh()
  },
  methods: {
    refresh() {
      const __this = this
      return new Promise((resolv, reject) => {
        this.$api.cluster
          .getClusterProps()
          .then(d => {
            resolv(d)
          })
          .catch(err => {
            __this.$message({
              type: 'error',
              message: locale.t('258')
            })
            reject(err)
          })
      })
    },
    submit(input) {
      return this.$api.cluster.updateClusterProps(input)
    }
  }
}
</script>
