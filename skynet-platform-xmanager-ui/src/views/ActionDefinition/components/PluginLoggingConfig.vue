<template>
  <kv-config
    :title="$t('260')"
    :showOperations="showOperations"
    :isCollapsable="isCollapsable"
    :initialCollapsed="initialCollapsed"
    :refreshFunc="refresh"
    :submitFunc="submit"
    ref="kvConfigRef"
  ></kv-config>
</template>
<script>
import locale from '@/i18n/index.js'

import KVConfig from './KVConfig'
export default {
  name: 'PluginLoggingConfig',
  components: {
    'kv-config': KVConfig
  },
  props: {
    pluginCode: {
      type: String
    },
    showOperations: {
      type: Boolean,
      default: true
    },
    isCollapsable: {
      type: Boolean,
      default: true
    },
    initialCollapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.$refs.kvConfigRef.refresh()
  },
  watch: {
    pluginCode(val) {
      if (val) {
        this.$refs.kvConfigRef.refresh()
      }
    }
  },
  methods: {
    refresh() {
      if (!this.pluginCode) {
        return Promise.resolve(null)
      }
      const __this = this
      return new Promise((resolv, reject) => {
        this.$api.plugin
          .getPluginLogging(this.pluginCode)
          .then(d => {
            resolv(d)
          })
          .catch(err => {
            __this.$message({
              type: 'error',
              message: locale.t('261')
            })
            reject(err)
          })
      })
    },
    submit(input) {
      if (!this.pluginCode) {
        return Promise.reject('pluginCode does not exist')
      }
      return this.$api.plugin.updatePluginLogging(this.pluginCode, input)
    }
  }
}
</script>
