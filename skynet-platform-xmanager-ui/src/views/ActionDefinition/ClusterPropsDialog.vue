<template>
  <el-dialog
    class="ele-mod"
    :visible.sync="dialogVisible"
    width="800px"
    top="30vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="$t('253')"
  >
    <prompt class="prompt-wrap">{{ $t('254') }}</prompt>
    <cluster-prop-config style="max-height:40vh;overflow:auto" :showTitle="false" :isCollapsable="false"></cluster-prop-config>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import ClusterPropConfig from './components/ClusterPropConfig'
import Prompt from '@/components/common/Prompt'
export default {
  name: 'ClusterPropsDialog',
  components: {
    'cluster-prop-config': ClusterPropConfig,
    prompt: Prompt
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.prompt-wrap {
  margin-bottom: 10px;
}
</style>
