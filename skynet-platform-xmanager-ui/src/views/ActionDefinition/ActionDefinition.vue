<template>
  <div class="action-def-manage" ref="actionDefinitionRef">
    <div class="top">
      <el-link v-permission:disabled="['admin']" target="_blank" type="primary" icon="el-icon-setting" @click="$refs.clusterPropsDialog.open()">{{ $t('198') }}</el-link>
      <el-link v-permission:disabled="['admin']" target="_blank" type="primary" icon="el-icon-setting" @click="$refs.clusterLoggingDialog.open()">{{ $t('199') }}</el-link>
      <el-link v-permission:disabled="['admin']" target="_blank" type="primary" icon="el-icon-upload" @click="onImport()">{{ $t('200') }}</el-link>
      <el-divider direction="vertical"></el-divider>
      <el-link target="_blank" type="primary" icon="el-icon-refresh" @click="refresh()">{{ $t('147') }}</el-link>
      <!-- <div class="clear"/> -->
    </div>

    <div class="main">
      <div class="plugins">
        <div class="header">
          <span class="title">{{ $t('201') }}</span>
        </div>
        <div class="body" v-loading="pluginsLoading" :element-loading-text="$t('202')">
          <div class="search">
            <el-input
              :placeholder="$t('203')"
              v-model="searchInput"
              size="mini"
              clearable
              @clear="pluginListFilter = null"
              @keyup.enter.native="onSearch"
            >
              <i slot="suffix" class="el-input__icon el-icon-search" @click="onSearch"></i>
            </el-input>
          </div>
          <div class="list">
            <div v-if="showSortable" class="sort-container">
              <draggable v-model="sortList" draggable=".sort-item">
                <div v-for="(item, index) in sortList" :key="index" class="sort-item">
                  <div class="left">{{ index + 1 }} . {{ item.name }}</div>
                  <div class="right">
                    <i class="el-icon-upload2" :title="$t('204')" @click="onSortUp(index)"></i>
                    <i class="el-icon-download" :title="$t('205')" @click="onSortDown(index)"></i>
                  </div>
                  <div class="clear" />
                </div>
              </draggable>
            </div>
            <el-tree
              v-else
              :data="treeData"
              :show-checkbox="showCheckbox"
              node-key="index"
              :current-node-key="0"
              :props="{ children: 'children', label: 'label' }"
              default-expand-all
              :expand-on-click-node="false"
              :highlight-current="true"
              @current-change="currentNodeChange"
              @check="onTreeCheck"
            >
              <span class="custom-tree-node" slot-scope="{ data }">
                <el-tooltip effect="light" :content="data.description" placement="top" :open-delay="data.description ? 500 : 10000000">
                  <span>{{ data.label }}</span>
                </el-tooltip>
                <el-dropdown v-if="data.index > -1 && !showCheckbox" @command="onDropdownCommand($event, data.index)">
                  <span class="dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="view">{{ $t('206') }}</el-dropdown-item>
                    <el-dropdown-item command="edit">{{ $t('64') }}</el-dropdown-item>
                    <el-dropdown-item command="export">{{ $t('207') }}</el-dropdown-item>
                    <el-dropdown-item command="delete">{{ $t('65') }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="footer">
          <div class="checkbox">
            <el-checkbox v-model="showCheckbox" :disabled="showSortable">{{ $t('208') }}</el-checkbox>
          </div>
          <div class="checkbox">
            <el-checkbox v-model="showSortable" :disabled="showCheckbox">{{ $t('209') }}</el-checkbox>
          </div>
          <div class="buttons">
            <el-tooltip :content="$t('210')" effect="light" placement="top" :open-delay="200">
              <el-button v-permission:disabled="['admin']" icon="el-icon-plus" plain size="mini" @click="$refs.pluginDetailDialog.open(true)" v-show="!showSortable"></el-button>
            </el-tooltip>
            <el-tooltip :content="$t('211')" :disabled="false" effect="light" placement="top" :open-delay="200">
              <el-button
                v-permission:disabled="['admin']"
                :title="$t('211')"
                icon="el-icon-download"
                plain
                size="mini"
                :disabled="!showCheckbox"
                @click="onExport"
                v-show="!showSortable"
              ></el-button>
            </el-tooltip>
            <el-tooltip :content="$t('212')" effect="light" placement="top" :open-delay="200">
              <el-button
                v-permission:disabled="['admin']"
                :title="$t('212')"
                icon="el-icon-delete"
                plain
                type="danger"
                size="mini"
                :disabled="!showCheckbox"
                @click="doDelete(selection)"
                v-show="!showSortable"
              >
              </el-button>
            </el-tooltip>
            <el-button type="primary" size="mini" icon="el-icon-check" @click="sortConfirm" v-show="showSortable">{{ $t('75') }}</el-button>
          </div>
        </div>
      </div>
      <div class="plugin-detail" :element-loading-text="$t('213')">
        <el-tabs v-model="activeTabName" class="height-inherit">
          <el-tab-pane :label="$t('214')" name="actionList" class="height-inherit" :lazy="true">
            <action-list
              :plugin="selectedPlugin"
              :noData="!pluginList || pluginList.length === 0"
              class="height-inherit"
              :showSortable="selectedPluginIndex !== -1"
              @updateReferencedFiles="updateReferencedFiles"
            ></action-list>
          </el-tab-pane>
          <el-tab-pane :label="$t('215')" name="repo" class="height-inherit" v-if="selectedPluginIndex > -1" :lazy="true">
            <repo :plugin="selectedPlugin" :referencedFiles="referencedFiles" class="height-inherit"></repo>
          </el-tab-pane>
          <el-tab-pane :label="$t('216')" name="configureProps" v-if="selectedPluginIndex > -1" :lazy="true">
            <props :pluginCode="selectedPlugin.code"></props>
          </el-tab-pane>
          <el-tab-pane :label="$t('217')" name="configureLogging" v-if="selectedPluginIndex > -1" :lazy="true">
            <logging :pluginCode="selectedPlugin.code"></logging>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="clear"></div>
    </div>
    <cluster-logging-dialog ref="clusterLoggingDialog"></cluster-logging-dialog>
    <cluster-props-dialog ref="clusterPropsDialog"></cluster-props-dialog>
    <plugin-detail-dialog ref="pluginDetailDialog" :successCallback="refresh"></plugin-detail-dialog>
    <confirm ref="deleteConfirmRef" :isDragVerify="true"></confirm>
    <input type="file" style="display: none" ref="fileInput" @change="onFileInputChange" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ActionList from './ActionList'
import Repository from './Repository'
import PluginProps from './PluginProps'
import PluginLogging from './PluginLogging'
import ClusterLoggingDialog from './ClusterLoggingDialog'
import ClusterPropsDialog from './ClusterPropsDialog'
import PluginDetailDialog from './PluginDetailDialog'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import draggable from 'vuedraggable'
export default {
  name: 'ActionDefinition',
  components: {
    'action-list': ActionList,
    repo: Repository,
    props: PluginProps,
    logging: PluginLogging,
    'cluster-logging-dialog': ClusterLoggingDialog,
    'cluster-props-dialog': ClusterPropsDialog,
    'plugin-detail-dialog': PluginDetailDialog,
    confirm: ConfirmDialog,
    draggable: draggable
  },
  created() {
    this.refresh()
  },
  mounted() {

    // /**
    //  * 计算初始时的最低高度，避免视图底下留空
    //  */
    // let appMainMinHeight = getAppMainMinHeight()
    // this.$refs.actionDefinitionRef.style.height = (appMainMinHeight - 20) + 'px'
  },
  data() {
    return {
      pluginList: null,
      activeTabName: 'actionList',
      searchInput: null,
      selectedPluginIndex: -1,
      pluginsLoading: false,
      showCheckbox: false,
      showSortable: false,
      selection: [],
      sortList: [],
      pluginListFilter: null,
      referencedFiles: []
    }
  },
  computed: {
    selectedPlugin() {
      // 返回{ code: '' }是因为pluginCode属性需要作为v-bind参数传给其他组件，undefined会报错。
      return this.pluginList && this.selectedPluginIndex !== null && this.selectedPluginIndex > -1
        ? this.pluginList[this.selectedPluginIndex]
        : {
          code: ''
        }
    },
    treeData() {
      const mapFunc = (v, index) => {
        let seq = index + 1
        return {
          index: v.__idxOfList,
          label: `${seq}. ${v.name} [${v.code}]`,
          description: v.description
        }
      }
      let list = this.pluginList || []
      let filteredPluginList = this.pluginListFilter ? list.filter(this.pluginListFilter) : list
      let children = filteredPluginList.map(mapFunc)
      return [
        {
          index: -1,
          label: locale.t('218'),
          children,
          description: ''
        }
      ]
    }
  },
  watch: {
    showSortable(val) {
      if (val) {
        this.sortList = this.pluginList
          ? this.pluginList.map(v => {
            return {
              name: v.name,
              code: v.code
            }
          })
          : []
      } else {
        this.sortList = []
      }
    }
  },
  methods: {
    refresh() {
      let thisVue = this
      this.selectedPluginIndex = -1
      this.pluginsLoading = true
      this.$api.plugin
        .getPlugins()
        .then(d => {
          if (d && d.length > 0) {
            thisVue.pluginList = d.map((val, idx) => {
              val.__idxOfList = idx
              return val
            })
            thisVue.selectedPluginIndex = 0
          } else {
            thisVue.selectedPluginIndex = -1
            thisVue.pluginList = []
          }
        })
        .catch(() => {
          thisVue.pluginList = null
          this.selectedPluginIndex = -1
        })
        .finally(() => {
          thisVue.pluginsLoading = false
        })
    },
    currentNodeChange(data) {
      this.selectedPluginIndex = data.index
    },
    onDropdownCommand(cmd, index) {
      let plugin = this.pluginList[index]
      if (cmd === 'view') {
        this.$refs.pluginDetailDialog.open(false, plugin)
      } else if (cmd === 'edit') {
        this.$refs.pluginDetailDialog.open(true, plugin)
      } else if (cmd === 'delete') {
        this.doDelete([plugin])
      } else if (cmd === 'export') {
        this.doExport([plugin])
      }
    },
    onTreeCheck(dataItem, treeCheckedStatus) {
      // console.log('%o, %o', dataItem, treeCheckedStatus)
      let indexes = treeCheckedStatus.checkedKeys.filter(i => {
        return i > -1
      })
      let set = new Set(indexes)
      this.selection = this.pluginList.filter((v, index) => {
        return set.has(index)
      })
    },
    doDelete(selection) {
      let names = selection.map(v => {
        return v.name
      })
      let thisVue = this
      let confirmCallback = function() {
        let promises = selection.map(v => {
          return this.$api.plugin.deletePlugin(v.code)
        })
        Promise.allSettled(promises).then(results => {
          let failedCount = results.filter(result => {
            return result.status === 'rejected'
          }).length
          let successCount = results.length - failedCount
          if (failedCount > 0) {
            thisVue.$message({
              message: `${locale.t('219')}${successCount}${locale.t('220')}${failedCount}${locale.t('221')}`,
              type: 'error'
            })
          } else {
            thisVue.$message({
              message: `${locale.t('222')}`,
              type: 'success'
            })
          }
          if (failedCount !== selection.length) {
            thisVue.refresh()
          }
        })
      }
      this.$refs.deleteConfirmRef.open(locale.t('223'), names, confirmCallback)
    },
    onExport() {
      if (this.selection && this.selection.length > 0) {
        this.doExport(this.selection)
      } else {
        this.$message.info(locale.t('224'))
      }
    },
    doExport(plugins) {
      let pluginCodes = plugins.map(v => {
        return v.code
      })
      this.$api.definition.exportActionDefinition(pluginCodes)
    },
    onFileInputChange(event) {
      let file = event.target.files[0]
      const __this = this
      this.$api.definition.importActionDefinition(file).then(() => {
        setTimeout(() => {
          __this.refresh()
        }, 1000)
      })
    },
    onImport() {
      this.$refs.fileInput.click()
    },
    onSortUp(index) {
      let item = this.sortList[index]
      let filtered = this.sortList.filter((v, idx) => {
        return idx !== index
      })
      this.sortList = [item].concat(filtered)
    },
    onSortDown(index) {
      let item = this.sortList[index]
      let filtered = this.sortList.filter((v, idx) => {
        return idx !== index
      })
      this.sortList = filtered.concat([item])
    },
    sortConfirm() {
      let codeList = this.sortList.map(v => v.code)
      this.$api.view.updatePluginOrder(codeList).then(() => {
        this.showSortable = false
        this.refresh()
      })
    },
    onSearch() {
      let input = this.searchInput ? this.searchInput.trim() : null
      if (input) {
        this.pluginListFilter = item => {
          if (!item.code || !item.name) {
            return true
          }
          return (
            item.code.toLocaleLowerCase().indexOf(input.toLocaleLowerCase()) > -1 ||
            item.name.toLocaleLowerCase().indexOf(input.toLocaleLowerCase()) > -1
          )
        }
      } else {
        this.pluginListFilter = null
      }
    },
    updateReferencedFiles(referencedFiles) {
      this.referencedFiles = referencedFiles
    }
  }
}
</script>
<style scoped lang="scss">
$footerHeight: 39px;
$headerHeight: 40px;

.action-def-manage {
  background-color: #ffffff;
  padding: 10px 15px;
  height: 100%;
}
.top {
  // height: 28px;
  height: 16px;
  margin-bottom: 10px;
  .el-link + .el-link {
    margin-left: 20px;
  }
  // .left{
  //   float:left;
  //   margin-right: 20px;
  // }
  // .right{
  //   float: right;
  //   margin-right: 15px;
  // }
}

.main {
  height: calc(100% - 26px);
}

.plugins {
  float: left;
  width: 350px;
  height: 100%;
  border: 1px solid $border-color-dark;
  border-right: none;
  .header {
    padding: 0 10px;
    height: $headerHeight;
    line-height: $headerHeight;
    background-color: rgb(229, 233, 242);
    font-size: 0;
    .title {
      font-size: 14px;
      display: inline-block;
      width: 290px;
    }
    i {
      display: inline-block;
      font-size: 18px;
      width: 40px;
      text-align: center;
    }
  }
  .body {
    padding: 10px;
    overflow: auto;
    height: calc(100% - #{$footerHeight} - #{$headerHeight});
    .search {
      // position: absolute;
      width: 329px;
    }

    .list {
      // margin-top: 28px;
      height: calc(100% - 28px);
      padding-top: 10px;

      .el-tree {
        height: 100%;
        overflow: auto;
        padding-right: 5px;
      }

      .sort-container {
        padding: 0 5px;
        width: 100%;
        height: calc(100% - 28px);
        overflow: auto;
        .sort-item {
          height: 22px;
          line-height: 16px;
          padding: 3px;
          font-size: 14px;
          background-color: rgb(239, 244, 253);
          &:hover {
            background-color: rgba(239, 244, 253, 0.5);
          }
          .left {
            float: left;
            width: calc(100% - 36px);
            cursor: move;
            text-overflow: ellipsis;
          }
          .right {
            font-size: 14px;
            float: left;
            width: 36px;
            color: rgb(54, 122, 224);
            i {
              cursor: pointer;
              &:hover {
                color: rgba(54, 122, 224, 0.5);
              }
            }
            // i + i{
            //   margin-left: 12px;
            // }
          }
        }
        .sort-item + .sort-item {
          margin-top: 5px;
        }
      }
    }
  }

  .footer {
    height: 39px;
    padding: 5px 10px;
    border-top: 1px solid $border-color-light;
    display: flex;
    justify-content: space-between;
    .checkbox {
      height: 28px;
      line-height: 28px;
    }
  }
}

.plugin-detail {
  float: left;
  width: calc(100% - 350px);
  height: 100%;
  padding: 0 15px;
  border: 1px solid $border-color-dark;
}
</style>
<style lang="scss">
.action-def-manage {
  .body .list {
    .el-tree-node__content {
      height: 30px;
      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 5px;

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 280px;
        }
      }
    }

    .is-leaf.el-tree-node__expand-icon {
      width: 0;
      height: 0;
      padding: 0;
    }
  }
  .plugin-detail {
    .el-tabs__content {
      height: calc(100% - 40px); /*.el-tabs__header height:40px, margin-bottom:0px */
      .el-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
    .el-tabs__header {
      margin-bottom: 0;
    }
  }
  .section-container + .section-container {
    margin-top: 40px;
    // border-top: 1px solid $border-color-light;
  }
}
</style>
<style lang="scss">
.action-def-manage .top .el-divider--vertical {
  margin: 0 15px;
  background-color: #c6cbd7;
}
</style>
