<template>
  <div class="wscn-http404-container">
    <div class="wscn-http404">
      <div class="pic-404">
        <img class="pic-404__parent" src="@/assets/404_images/404.png" alt="404" />
      </div>
      <div class="oops">
        <div class="oops__title">{{ title }}</div>
        <div class="oops__message">{{ message }}</div>
        <a href="" v-if="showGoHome" class="oops__return-home">{{ $t('1049') }}</a>
      </div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'Page404',
  props: {
    showGoHome: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: locale.t('1050')
    },
    message: {
      type: String,
      default: 'Not Found'
    }
  },
  computed: {}
}
</script>
<style scoped lang="scss">
.wscn-http404-container {
  transform: translate(-50%, -50%);
  position: absolute;
  top: 40%;
  left: 50%;
}
.wscn-http404 {
  position: relative;
  width: 900px;
  overflow: hidden;
  .pic-404 {
    position: relative;
    float: left;
    width: 500px;
    overflow: hidden;
    &__parent {
      width: 100%;
    }
    &__child {
      position: absolute;
    }
  }
  .oops {
    position: relative;
    float: left;
    width: 300px;
    padding: 50px 0;
    overflow: hidden;
    &__title {
      font-size: 24px;
      font-weight: bold;
      line-height: 40px;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__message {
      font-size: 24px;
      line-height: 40px;
      color: #222;
      font-weight: bold;
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 13px;
      line-height: 21px;
      color: grey;
      opacity: 0;
      margin-bottom: 30px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    &__return-home {
      display: block;
      float: left;
      width: 110px;
      height: 36px;
      background: #1482f0;
      border-radius: 100px;
      text-align: center;
      color: #ffffff;
      opacity: 0;
      font-size: 14px;
      line-height: 36px;
      cursor: pointer;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
</style>
