<template>
  <div class="permission-test-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限控制测试页面</span>
        <el-tag :type="isAdmin ? 'success' : 'warning'" style="float: right;">
          当前角色: {{ currentRoles.join(', ') }}
        </el-tag>
      </div>

      <!-- 用户信息显示 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-alert
            :title="`当前用户: ${userName}, 角色: ${currentRoles.join(', ')}`"
            :type="isAdmin ? 'success' : 'info'"
            :closable="false">
          </el-alert>
        </el-col>
      </el-row>

      <!-- 权限控制示例 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>隐藏模式 (v-permission)</h3>
          <p>非admin用户看不到以下按钮：</p>
          <div style="margin-bottom: 10px;">
            <el-button type="primary" v-permission:disabled="['admin']">仅Admin可见</el-button>
            <el-button type="success" v-permission="['admin', 'editor']">Admin或Editor可见</el-button>
            <el-button type="info" v-permission="['viewer', 'admin']">Viewer或Admin可见</el-button>
          </div>
        </el-col>

        <el-col :span="12">
          <h3>禁用模式 (v-permission:disabled)</h3>
          <p>非admin用户可以看到但无法操作以下按钮：</p>
          <div style="margin-bottom: 10px;">
            <el-button type="primary" v-permission:disabled="['admin']" @click="handleAdminAction">仅Admin可操作</el-button>
            <el-button type="success" v-permission:disabled="['admin', 'editor']" @click="handleEditorAction">Admin或Editor可操作</el-button>
            <el-button type="info" v-permission:disabled="['viewer', 'admin']" @click="handleViewerAction">Viewer或Admin可操作</el-button>
          </div>
        </el-col>
      </el-row>

      <!-- 操作按钮组 -->
      <el-divider content-position="left">常见操作按钮</el-divider>
      <el-row :gutter="10">
        <el-col :span="4">
          <el-button type="primary" icon="el-icon-plus" v-permission:disabled="['admin']" @click="handleCreate">新建</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="warning" icon="el-icon-edit" v-permission:disabled="['admin']" @click="handleEdit">编辑</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="danger" icon="el-icon-delete" v-permission:disabled="['admin']" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="success" icon="el-icon-check" v-permission:disabled="['admin']" @click="handleSave">保存</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="info" icon="el-icon-upload" v-permission:disabled="['admin']" @click="handleImport">导入</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" icon="el-icon-download" v-permission:disabled="['admin']" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 表格操作示例 -->
      <el-divider content-position="left">表格操作示例</el-divider>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="名称" width="180"></el-table-column>
        <el-table-column prop="status" label="状态" width="180"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" v-permission:disabled="['admin']" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" v-permission:disabled="['admin']" @click="handleTableEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" v-permission:disabled="['admin']" @click="handleTableDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 角色切换测试 -->
      <el-divider content-position="left">角色切换测试</el-divider>
      <el-alert
        title="注意：以下功能仅用于测试，实际生产环境中角色由后端认证系统控制"
        type="warning"
        :closable="false"
        style="margin-bottom: 10px;">
      </el-alert>
      <el-button @click="switchToAdmin">切换到Admin角色</el-button>
      <el-button @click="switchToViewer">切换到Viewer角色</el-button>
      <el-button @click="refreshUserInfo">刷新用户信息</el-button>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'PermissionTest',
  data() {
    return {
      tableData: [
        { name: '配置项1', status: '启用' },
        { name: '配置项2', status: '禁用' },
        { name: '配置项3', status: '启用' }
      ]
    }
  },
  computed: {
    ...mapGetters(['roles', 'name']),
    currentRoles() {
      return this.roles || []
    },
    userName() {
      return this.name || 'Unknown'
    },
    isAdmin() {
      return this.currentRoles.includes('admin')
    }
  },
  methods: {
    handleAdminAction() {
      this.$message.success('Admin操作执行成功')
    },
    handleEditorAction() {
      this.$message.success('Editor操作执行成功')
    },
    handleViewerAction() {
      this.$message.success('Viewer操作执行成功')
    },
    handleCreate() {
      this.$message.success('新建操作')
    },
    handleEdit() {
      this.$message.success('编辑操作')
    },
    handleDelete() {
      this.$message.success('删除操作')
    },
    handleSave() {
      this.$message.success('保存操作')
    },
    handleImport() {
      this.$message.success('导入操作')
    },
    handleExport() {
      this.$message.success('导出操作')
    },
    handleView(row) {
      this.$message.info(`查看: ${row.name}`)
    },
    handleTableEdit(row) {
      this.$message.success(`编辑: ${row.name}`)
    },
    handleTableDelete(row) {
      this.$message.error(`删除: ${row.name}`)
    },
    // 测试用的角色切换方法
    switchToAdmin() {
      this.$store.commit('user/SET_ROLES', ['admin'])
      this.$message.success('已切换到Admin角色')
    },
    switchToViewer() {
      this.$store.commit('user/SET_ROLES', ['viewer'])
      this.$message.success('已切换到Viewer角色')
    },
    refreshUserInfo() {
      this.$store.dispatch('user/getInfo').then(() => {
        this.$message.success('用户信息已刷新')
      }).catch(error => {
        this.$message.error('刷新用户信息失败: ' + error.message)
      })
    }
  }
}
</script>

<style scoped>
.permission-test-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
