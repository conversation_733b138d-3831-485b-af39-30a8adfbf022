<template>
  <div>
    <el-popover placement="top-start" trigger="hover">
      <div class="label-contain">
        <LabelTag :label="labels" @update="updateLables" />
      </div>
      <el-tag size="small" slot="reference" class="tag-num">
        <span>{{ `${labelListLength}${$t('476')}` }}</span>
        <i class="el-icon-edit"></i>
      </el-tag>
    </el-popover>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import LabelTag from '@/components/LabelTag/index.vue'
export default {
  name: 'labels',
  components: {
    LabelTag
  },
  props: ['labels'],
  data() {
    return {
      labelListLength: 0
    }
  },
  watch: {
    labels: {
      handler(val, oldVal) {
        let length = 0
        if (val) {
          for (const key in val) {
            if (Object.hasOwnProperty.call(val, key)) {
              length++
            }
          }
        }
        this.labelListLength = length
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    updateLables(data, callback) {
      this.$emit('update:labels', data)
      callback()
    }
  }
}
</script>
<style scoped lang="scss">
.label-contain {
  max-width: 600px;
}
.tag-num {
  cursor: pointer;
  margin-left: 5px;
}
</style>
