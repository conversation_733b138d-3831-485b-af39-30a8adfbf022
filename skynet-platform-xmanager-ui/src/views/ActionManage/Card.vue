<template>
  <div class="card">
    <div class="card__header">
      <div class="left">
        <div class="title">
          <span style="margin-right:10px">[{{ title.seq }}]</span>
          <i :class="title.titleIcon" />
          <a @click="$emit('title-click', title)">{{ title.titleText }}</a>
        </div>
        <div v-show="!collapsedStatus" class="display-mode">
          <span class="label">{{ $t('480') }}</span>
          <el-radio-group size="tiny" v-model="displayMode">
            <el-radio-button label="group">{{ $t('481') }}</el-radio-button>
            <el-radio-button label="tile">{{ $t('482') }}</el-radio-button>
            <el-radio-button label="collapsed">{{ $t('483') }}</el-radio-button>
          </el-radio-group>
        </div>
        <div v-show="!collapsedStatus" class="check-mode">
          <span class="label">{{ $t('484') }}</span>
          <el-switch v-model="onCheck" active-color="rgba(103, 194, 58,0.7)" inactive-color="rgba(122, 133, 153, 1)" />
        </div>
        <div v-show="onCheck" class="all-check">
          <el-checkbox v-model="allChecked">{{ $t('485') }}</el-checkbox>
        </div>
      </div>
      <div class="right">
        <div class="alloc">
          <el-button
            v-if="cardType === 'server'"
            class="refresh-btn"
            type="primary"
            plain
            icon="el-icon-refresh"
            @click="refresh()"
            :loading="loading"
            >{{ $t('147') }}</el-button
          >
          <!-- <span style="vertical-align:middle"> -->
          <el-button v-permission:disabled="['admin']" size="tiny" plain @click="onAllocClick">{{ $t('464') }}</el-button>
          <!-- </span> -->
        </div>
        <div class="collapse" @click="onCollapseClick">
          <span>{{ collapseText }}</span>
          <i :class="collapseIcon" />
        </div>
      </div>
      <div class="clear"></div>
    </div>
    <div v-if="showCardBody" class="card__body" v-loading="loading">
      <div v-if="displayMode === 'tile'" class="badge-list">
        <action-badge
          v-for="(badge, index) in badgeList"
          :key="index"
          :text="'[' + (index + 1) + '] ' + badge.text"
          :colorType="badge.colorType"
          :event="badge.event"
          :showCheck="onCheck"
          ref="actionBadgeRefs"
        />
      </div>
      <div v-else class="badge-group-list">
        <action-badge-group
          v-for="(badgeGroup, index) in badgeGroupList"
          :key="index"
          :name="badgeGroup.groupName"
          :list="badgeGroup.items"
          :showCheck="onCheck"
          :cardDisplayMode="displayMode"
          ref="actionBadgeGroupRefs"
        />
      </div>
    </div>
    <div class="card__footer">
      <slot name="card__footer"></slot>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ActionBadge from './ActionBadge'
import ActionBadgeGroup from './ActionBadgeGroup'
// import EventBus from '@/components/event/EventBus'
import { mapGetters } from 'vuex'
import { isArray } from '@/utils/validate'
export default {
  name: 'card',
  components: {
    'action-badge': ActionBadge,
    'action-badge-group': ActionBadgeGroup
  },
  data() {
    return {
      // loading: false,
      displayMode: 'group',
      onCheck: false,
      allChecked: false,
      collapsedStatus: false
    }
  },
  props: {
    title: {
      type: Object,
      required: true
    },
    badgeList: {
      type: Array,
      required: true
    },
    badgeGroupList: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      required: true
    },
    cardType: {
      type: String
    }
  },
  watch: {
    onCheck(val) {
      if (!val) {
        this.allChecked = false
      }
    },
    allChecked(val) {
      if (this.$refs.actionBadgeRefs) {
        for (let ref of this.$refs.actionBadgeRefs) {
          ref.updateChecked(val)
        }
      }
      if (this.$refs.actionBadgeGroupRefs) {
        for (let ref of this.$refs.actionBadgeGroupRefs) {
          ref.updateChecked(val)
        }
      }
    },
    cardCollapsed(val) {
      this.collapsedStatus = val
    }
  },
  computed: {
    ...mapGetters(['cardCollapsed']),
    collapseText() {
      return this.collapsedStatus ? locale.t('364') : locale.t('365')
    },
    collapseIcon() {
      return this.collapsedStatus ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    },
    /**
     * 因为.card__body存在padding，当.card__body内不包含元素时，会显示出一个高度为padding-bottom的空白，所以不含元素时干脆不显示.card__body。
     */
    showCardBody() {
      if (this.collapsedStatus) {
        return false
      }
      if (this.displayMode === 'tile') {
        if (!this.badgeList || this.badgeList.length === 0) {
          return false
        }
      } else {
        if (!this.badgeGroupList || this.badgeGroupList.length === 0) {
          return false
        }
      }
      return true
    }
  },
  methods: {
    onCollapseClick() {
      this.collapsedStatus = !this.collapsedStatus
    },
    onAllocClick() {
      this.$emit('alloc-click')
    },
    refresh() {
      this.$emit('refresh', this.title.titleText)
    }
  }
}
</script>
<style scoped lang="scss">
$titleFontSize: 14px;
$mainFontSize: 12px;
$headerElementHeight: 32px;
$headerPadding: 10px;
$headerBackColor: #fafbfc;

.card {
  font-size: 0; //父元素font-size设为0可以避免 inline-display元素因为源码空格换行导致的间隙
  // box-shadow: 0px 0px 3px 1px rgba(0,0,0,0.08);
  width: 100%;
  .card__header {
    border: 1px solid $border-color-light;
    background-color: $headerBackColor;
    .left {
      font-size: $mainFontSize;
      padding-left: $headerPadding;
      height: $headerElementHeight;
      line-height: $headerElementHeight;
      float: left;
      width: 75%;
      .title {
        font-size: $titleFontSize;
      }
      /* 设置radiogroup垂直居中 */
      .display-mode .el-radio-group {
        vertical-align: top;
        margin-top: calc((#{$headerElementHeight} - 24px) / 2);
      }
      .display-mode,
      .check-mode,
      .all-check {
        margin-left: 20px;
        .label {
          margin-right: 10px;
        }
      }
    }
    .right {
      font-size: $mainFontSize;
      height: $headerElementHeight;
      line-height: $headerElementHeight;
      padding-right: $headerPadding;
      float: right;
      width: 25%;
      text-align: right;

      .alloc .el-button {
        vertical-align: top;
        margin-top: calc((#{$headerElementHeight} - 24px) / 2);
        height: 24px;
      }
      .refresh-btn {
        padding: 3px 10px;
      }
      .collapse {
        padding: 0 10px;
        cursor: pointer;
      }
      .collapse:hover {
        color: $el-color-primary;
      }
      & > div {
        margin-left: 20px;
      }
    }
    .left > div,
    .right > div {
      display: inline-block;
      vertical-align: top;
    }
  } //end define of .card__header

  .card__body {
    padding: 10px 10px 0 0;
    // border-left: 1px solid $border-color-light;
    // border-right: 1px solid $border-color-light;
    border: 1px solid $border-color-light;
    border-top: none;
  }

  .card__footer {
    // padding: 5px 10px;
    line-height: 35px;
    padding: 0 0 0 10px;
    height: 36px;
    border: 1px solid $border-color-light;
    border-top: none;
    .server-info {
      > span {
        margin-right: 30px;
      }
    }
    .server-tags {
      .el-tag {
        margin-right: 10px;
        font-size: 12px;
      }
    }
  }

  .card__body,
  .card__footer {
    background-color: #ffffff;
    font-size: 12px;
    overflow: hidden;
  }

  .action-badge {
    margin: 10px 0 0 0;
  }
  .action-badge + .action-badge {
    // margin-left: 5px;
  }

  .badge-group {
    display: inline-block;
    // margin: 5px 0 0 0;
  }

  .badge-group + .badge-group {
    margin-left: 5px;
  }
}
</style>
<style lang="scss">
.card {
  /**
    /* 增加自定义tiny size，调节单选控件大小，高度20px;
    */
  .el-radio-button--tiny {
    .el-radio-button__inner {
      padding: 5px 10px;
      font-size: 12px;
    }
  }

  /**
    /* 调节switch大小
     */
  .el-switch__core {
    height: 16px !important;
    width: 35px !important;
  }
  .el-switch__core::after {
    height: 12px !important;
    width: 12px !important;
  }
  .el-switch.is-checked .el-switch__core::after {
    margin-left: -14px !important;
  }

  &:hover {
    box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.15);
  }
}
</style>
