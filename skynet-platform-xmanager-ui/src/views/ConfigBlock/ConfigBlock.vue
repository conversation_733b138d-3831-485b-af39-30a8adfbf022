<template>
  <el-container class="block-container config-block">
    <el-aside width="350px" class="block-aside writeBg">
      <div class="search-box">
        <el-input class="search" v-model="filterWord" :placeholder="$t('1030')" clearable size="small">
          <i slot="suffix" class="el-input__icon el-icon-search"></i
        ></el-input>
        <el-tooltip class="item" effect="dark" :content="$t('147')" placement="top-start">
          <span class="add-btn" @click="refreshList">
            <i class="el-icon-refresh"></i>
          </span>
        </el-tooltip>
      </div>
      <ul class="list-box">
        <draggable :list="blockListFiltered" draggable=".sort-item">
          <li
            v-for="(item, index) of blockListFiltered"
            class="list-item"
            :class="{ active: activeItem.code === item.code, 'sort-item': showSortable }"
            :key="index"
            @click.stop="onSelect(item.code)"
          >
            <el-checkbox v-if="showCheckbox" v-model="item.checked">{{ index + 1 }}. {{ item.name }} [{{ item.code }}]</el-checkbox>
            <span v-else>{{ index + 1 }}. {{ item.name }} [{{ item.code }}]</span>
            <span class="del-btn" @click="onDelete(item)">
              <i class="el-icon-delete"></i>
            </span>
          </li>
        </draggable>
      </ul>
      <div class="footer">
        <div class="checkbox">
          <el-checkbox v-model="showCheckbox" v-show="!showSortable">{{ $t('208') }}</el-checkbox>
        </div>
        <div class="checkbox">
          <el-checkbox v-model="showSortable" :disabled="showCheckbox">{{ $t('209') }}</el-checkbox>
        </div>
        <div class="buttons">
          <el-tooltip :content="$t('1031')" effect="light" placement="top" :open-delay="200">
            <el-button
              v-permission:disabled="['admin']"
              icon="el-icon-plus"
              plain
              size="mini"
              @click="onNew"
              v-show="!showSortable"
              :disabled="showSortable || showCheckbox"
            ></el-button>
          </el-tooltip>

          <el-tooltip :content="$t('1032')" :disabled="false" effect="light" placement="top" :open-delay="200">
            <el-button
              v-permission:disabled="['admin']"
              :title="$t('1032')"
              icon="el-icon-upload"
              plain
              size="mini"
              :disabled="showCheckbox"
              @click="onImport"
              v-show="!showSortable"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('1033')" :disabled="false" effect="light" placement="top" :open-delay="200">
            <el-button
              v-permission:disabled="['admin']"
              :title="$t('1033')"
              icon="el-icon-download"
              plain
              size="mini"
              :disabled="noneChecked"
              @click="onExport"
              v-show="!showSortable"
            ></el-button>
          </el-tooltip>

          <el-tooltip :content="$t('1034')" effect="light" placement="top" :open-delay="200">
            <el-button
              v-permission:disabled="['admin']"
              :title="$t('1034')"
              icon="el-icon-delete"
              plain
              type="danger"
              size="mini"
              :disabled="noneChecked"
              @click="onDeletes()"
              v-show="!showSortable"
            >
            </el-button>
          </el-tooltip>
          <el-button type="primary" size="mini" icon="el-icon-check" @click="sortConfirm" v-show="showSortable">{{ $t('75') }}</el-button>
        </div>
      </div>
    </el-aside>
    <div class="main">
      <div class="main-top writeBg flexBox">
        <h3>{{ activeItem.name }} [{{ activeItem.code }}]</h3>
        <div v-if="isEdit">
          <!-- 保存按钮：admin权限，非admin用户禁用显示 -->
          <el-button icon="el-icon-check" type="primary" @click="onSave" v-permission:disabled="['admin']">{{ $t('88') }}</el-button>
          <!-- 取消按钮：admin权限，非admin用户禁用显示 -->
          <el-button icon="el-icon-close" @click="onCancel" v-permission:disabled="['admin']">{{ $t('76') }}</el-button>
        </div>
        <div v-else>
          <!-- 编辑按钮：admin权限，非admin用户禁用显示 -->
          <el-button icon="el-icon-edit" type="primary" @click="isEdit = true" v-permission:disabled="['admin']">{{ $t('64') }}</el-button>
        </div>
      </div>
      <div class="main-body writeBg">
        <el-form :model="activeItem" label-width="100px" size="mini" ref="configBlockForm" label-position="right" :rules="rules">
          <div :title="$t('277')" v-loading="loading">
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('1035')" prop="name">
                  <el-input v-if="isEdit" v-model="activeItem.name" :placeholder="$t('1036')"></el-input>
                  <div v-else class="not-edit">{{ activeItem.name }}</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('1037')" prop="code">
                  <el-input :placeholder="$t('1038')" v-if="isEdit" v-model="activeItem.code"></el-input>
                  <div v-else class="not-edit">{{ activeItem.code }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('1039')" prop="text">
                  <div class="right-prism-box" :class="{ 'right-prism-box__edit': isEdit }">
                    <prism-editor
                      class="my-prism-editor"
                      :class="isEdit ? 'edit-mode' : ''"
                      :readonly="!isEdit"
                      v-model="activeItem.text"
                      :highlight="highlighter"
                      line-numbers
                    ></prism-editor>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
    <div>
      <input type="file" style="display: none" ref="fileInput" @change="onFileInputChange" />
    </div>
  </el-container>
</template>
<script>
import locale from '@/i18n/index.js'

import draggable from 'vuedraggable'
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-properties'
import 'prismjs/themes/prism.css'
export default {
  name: 'ConfigBlock',
  components: {
    draggable: draggable
  },
  data() {
    let rules = {
      name: [
        {
          required: true,
          message: locale.t('1040'),
          trigger: 'blur'
        }
      ],
      code: [
        {
          required: true,
          message: locale.t('1041'),
          trigger: 'blur'
        },
        {
          pattern: /^[0-9a-zA-Z-_\.]+$/,
          message: locale.t('323'),
          trigger: 'blur'
        }
      ]
    }
    return {
      activeItem: {},
      blockList: [],
      selectCode: '',
      filterWord: '',
      context: 9999,
      showSortable: false,
      showCheckbox: false,
      loading: false,
      isEdit: false,
      rules
    }
  },
  computed: {
    blockListFiltered() {
      return this.filterList(this.blockList, this.filterWord)
    },
    emptyItem() {
      return {
        name: locale.t('1042'),
        code: 'config-01',
        text: '',
        index: '0'
      }
    },
    noneChecked() {
      return this.blockList.filter(x => x.checked).length === 0
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getList()
  },
  methods: {
    highlighter(data) {
      return highlight(data, languages.properties)
    },
    onSearch() {
      return this.filterList(this.blockList, this.filterWord)
    },
    onSave() {
      this.$refs.configBlockForm
        .validate()
        .then(() => {
          this.$api.config.saveBlock(this.activeItem).then(res => {
            this.isEdit = false
            this.selectCode = this.activeItem.code
            this.getList()
          })
        })
        .catch(() => {
          // console.error('校验失败')
        })
    },
    onCancel() {
      if (this.selectCode) {
        this.onSelect(this.selectCode)
      }
      this.isEdit = false
    },
    onImport() {
      this.$refs.fileInput.click()
    },
    onExport() {
      var codes = this.blockList.filter(x => x.checked).map(x => x.code)
      if (!codes) {
        return
      }
      this.$api.config.exportBlock(codes).then(res => {
        this.$message.success(`${locale.t('1043')}`)
      })
    },
    getList() {
      this.loading = true
      this.$api.config.getBlocks().then(res => {
        this.blockList = res
        if (this.blockList.length > 0 && !this.selectCode && !this.activeItem.code) {
          this.selectCode = this.blockList[0].code
        }
        this.onSelect(this.selectCode)
        this.loading = false
      })
    },
    refreshList() {
      this.filterWord = ''
      this.getList()
    },
    onSelect(code) {
      this.isEdit = false
      if (code && !this.showCheckbox) {
        this.loading = true
        this.$api.config.getBlock(code).then(res => {
          this.activeItem = res
          this.activeItem.text = this.filterLastLine(this.activeItem.text)
          this.selectCode = res.code
          this.loading = false
        })
      }
    },
    onNew() {
      this.isEdit = true
      this.activeItem = {
        name: locale.t('1044') + this.blockList.length,
        code: 'code' + this.blockList.length,
        text: locale.t('1045'),
        index: '0'
      }
    },
    onDeletes() {
      var codes = this.blockList.filter(x => x.checked).map(x => x.code)
      if (!codes) {
        return
      }
      this.$confirm(`${locale.t('1046')}`, locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.$api.config.deleteBlocks(codes).then(res => {
          this.$message.success(`${locale.t('1029')}`)
          this.activeItem = {
            name: '',
            code: '',
            text: '',
            index: 0
          }
          this.selectCode = ''
          this.getList()
          this.showCheckbox = false
        })
      })
    },
    onDelete(item) {
      this.$confirm(`${locale.t('1047')}${item.name} [${item.code}${locale.t('1048')}`, locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.$api.config.deleteBlock(item.code).then(res => {
          this.$message.success(`${locale.t('1029')}`)
          this.activeItem = {
            name: '',
            code: '',
            text: '',
            index: 0
          }
          this.selectCode = ''
          this.getList()
        })
      })
    },
    sortConfirm() {
      let codes = this.blockList.map(x => x.code)
      this.$api.config.sortBlock(codes).then(res => {
        this.showSortable = false
      })
    },
    onFileInputChange(event) {
      let file = event.target.files[0]
      const __this = this
      this.$api.config.importBlock(file).then(() => {
        setTimeout(() => {
          __this.refreshList()
        }, 500)
      })
    },
    filterList(list, word) {
      return word ? list.filter(x => x.name.includes(word) || x.code.includes(word)) : list
    },
    filterLastLine(data) {
      let ret = data || ''
      // 去除最后一个\n（后台提供数据的缺陷)
      if (ret.length > 0 && ret[ret.length - 1] === '\n') {
        ret = ret.substring(0, ret.length - 1)
      }
      return ret
    }
  }
}
</script>
<style lang="scss">
.config-block {
  .CodeMirror {
    // height: calc(100vh - 320px);
    border-radius: 5px;
  }
}

.writeBg {
  background: #ffff;
  border: 1px solid #e2e7ee;
  border-radius: 5px;
}

.flexBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.block-container {
  height: 100%;

  .block-aside {
    height: 100%;
    padding: 0 0;

    .search-box {
      padding: 16px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search {
        width: calc(100% - 30px);
      }

      .add-btn {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 32px;
        height: 32px;
        border: 1px solid #e2e7ee;
        border-radius: 3px;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .list-box {
      height: calc(100% - 64px - 43px);
      overflow: auto;

      .list-item {
        height: 44px;
        line-height: 44px;
        padding: 0px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        font-size: 14px;

        .del-btn {
          display: none;
        }

        &.sort-item {
          cursor: move;
        }
      }

      .list-item.active {
        border-left: 5px solid #4b72ef;
        background: #eff4fd;
        padding-left: 11px;
      }

      .list-item:hover {
        background: #f2f3f5;

        .del-btn {
          display: block;
        }
      }
    }
  }

  .main {
    width: calc(100% - 350px);
    height: 100%;
    padding-left: 16px;

    .main-top {
      padding: 10px 16px;

      > h3 {
        font-weight: bold;
      }
    }

    .main-body {
      padding: 10px 16px;
      margin-top: 16px;
      height: calc(100% - 64px);
      overflow-y: auto;
    }
  }
}

.d2h-file-side-diff {
  margin-bottom: -5px;
  max-height: 500px;
  overflow: scroll;
}

.d2h-code-side-line {
  padding: 0 0;
}

.d2h-code-side-linenumber {
  padding: 0 0.5rem;
  position: relative;
  line-height: 20px;
  height: 20px;
}

.footer {
  // height: 39px;
  padding: 5px 10px;
  border-top: 1px solid $border-color-light;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .checkbox {
    height: 28px;
    line-height: 28px;
  }
}

.right-prism-box {
  border: 1px dashed #dfe1e6;
  padding: 5px 15px;

  &__edit {
    border: 1px solid #dfe1e6;
  }
}

.edit-mode {
  background-color: #efefef;
}
</style>
