import locale from '@/i18n/index.js'
import { getToken, setToken, removeToken, encryptPassword } from '@/utils/auth'
import jwt_decode from 'jwt-decode'
import { resetRouter } from '@/router'
import { login, getUserInfo } from '@/axios/api/auth'
const state = {
  token: getToken(),
  name: '',
  introduction: '',
  roles: []
}
const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  }
}
const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      let encryption = encryptPassword(password)
      login({
        username: username.trim(),
        password: encryption
      })
        .then(resp_body => {
          commit('SET_TOKEN', resp_body.token)
          setToken(resp_body.token)

          // 解析JWT token中的角色信息
          try {
            const decodedToken = jwt_decode(resp_body.token)
            console.log('Decoded JWT token:', decodedToken)

            let roles = ['viewer'] // 默认角色
            if (decodedToken.authorities && Array.isArray(decodedToken.authorities)) {
              // 处理角色信息，去掉ROLE_前缀并转为小写
              roles = decodedToken.authorities
                .filter(auth => typeof auth === 'string' && auth.startsWith('ROLE_'))
                .map(auth => auth.substring(5).toLowerCase())

              if (roles.length === 0) {
                roles = ['viewer']
              }
            }

            commit('SET_ROLES', roles)
            console.log('User roles set:', roles)
          } catch (error) {
            console.error('Failed to decode JWT token:', error)
            commit('SET_ROLES', ['viewer']) // 默认角色
          }

          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 优先从后端获取用户信息
      getUserInfo()
        .then(response => {
          const data = response.data || response

          // 验证返回的数据结构
          if (!data || !data.roles || !Array.isArray(data.roles)) {
            console.error('Invalid user info response:', data)
            // 如果后端返回的数据格式不正确，使用state中的角色信息
            const fallbackData = {
              roles: state.roles.length > 0 ? state.roles : ['viewer'],
              name: data?.username || state.name || 'User',
              introduction: data?.introduction || state.introduction || 'User'
            }
            commit('SET_ROLES', fallbackData.roles)
            commit('SET_NAME', fallbackData.name)
            commit('SET_INTRODUCTION', fallbackData.introduction)
            resolve(fallbackData)
            return
          }

          // 设置用户信息到store
          commit('SET_ROLES', data.roles)
          commit('SET_NAME', data.name || data.username || 'User')
          commit('SET_INTRODUCTION', data.introduction || 'User')

          console.log('User info loaded from backend:', data)
          resolve(data)
        })
        .catch(error => {
          console.error('Failed to get user info from backend:', error)

          // 如果后端接口失败，使用state中已有的角色信息
          if (state.roles && state.roles.length > 0) {
            const stateData = {
              roles: state.roles,
              name: state.name || 'User',
              introduction: state.introduction || 'User'
            }
            console.log('Using roles from state:', stateData)
            resolve(stateData)
          } else {
            // 如果state中也没有角色信息，尝试从JWT token中解析
            try {
              const token = getToken()
              if (token) {
                const decoded = jwt_decode(token)
                console.log('Fallback: Decoded JWT token:', decoded)

                let roles = ['viewer'] // 默认角色
                if (decoded.authorities && Array.isArray(decoded.authorities)) {
                  roles = decoded.authorities
                    .filter(auth => typeof auth === 'string' && auth.startsWith('ROLE_'))
                    .map(auth => auth.substring(5).toLowerCase())

                  if (roles.length === 0) {
                    roles = ['viewer']
                  }
                }

                const fallbackData = {
                  roles: roles,
                  name: decoded.sub || 'User',
                  introduction: 'User'
                }

                commit('SET_ROLES', fallbackData.roles)
                commit('SET_NAME', fallbackData.name)
                commit('SET_INTRODUCTION', fallbackData.introduction)

                console.log('User info from JWT fallback:', fallbackData)
                resolve(fallbackData)
              } else {
                reject(new Error('No token available'))
              }
            } catch (jwtError) {
              console.error('Failed to decode JWT token:', jwtError)
              reject(error)
            }
          }
        })
    })
  },
  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise(resolve => {
      // 退出登陆
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resetRouter()
      dispatch('tagsView/delAllViews', null, {
        root: true
      })
      resolve()
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
