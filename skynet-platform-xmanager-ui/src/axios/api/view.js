import locale from '@/i18n/index.js'
import request from '../request'
function getMenus() {
  return request.get('skynet/api/v3/view/menus')
}
function getGrafanaAddr() {
  return request.get('skynet/api/v3/view/grafana/address')
}
function updatePluginOrder(codeList) {
  return request.put('skynet/api/v3/view/order?type=plugin', codeList, {
    __view_pop_success: locale.t('52'),
    __view_pop_error: locale.t('53')
  })
}
function updateAgentOrder(ipList) {
  return request.put('skynet/api/v3/view/order?type=agent', ipList, {
    __view_pop_success: locale.t('54'),
    __view_pop_error: locale.t('55')
  })
}
function updateActionOrder(pluginCode, actionCodeList) {
  return request.put(`skynet/api/v3/view/order?type=action-definition&plugin=${pluginCode}`, actionCodeList, {
    __view_pop_success: locale.t('56'),
    __view_pop_error: locale.t('57')
  })
}
export default {
  getMenus,
  getGrafanaAddr,
  updatePluginOrder,
  updateAgentOrder,
  updateActionOrder
}
