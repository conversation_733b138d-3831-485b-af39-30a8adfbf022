import locale from '@/i18n/index.js'
import request from '../request'

/**
 * 用户登录
 * @param {Object} data 登录信息
 * @returns {Promise}
 */
export function login(data) {
  return request.post('skynet/auth/login', data)
}

/**
 * 刷新token
 * @returns {Promise}
 */
export function refreshToken() {
  return request.get('skynet/auth/refresh')
}

/**
 * 修改密码
 * @param {Object} data 密码信息
 * @returns {Promise}
 */
export function updatePwd(data) {
  return request.post('skynet/auth/updatePwd', data, {
    __view_pop_success: locale.t('16')
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return request.get('skynet/auth/userinfo')
}
