import locale from '@/i18n/index.js'
import axios from 'axios'
import store from '@/store'
import router from '@/router'
import { ELMessage } from '@/common/util'
const timeout = process.env.VUE_APP_REQ_TIMEOUT ? parseInt(process.env.VUE_APP_REQ_TIMEOUT) : 30000

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL,
  timeout // request timeout
})

// 为了避免重复弹出提示框，初始化一个锁
let redirectLock = false
service.interceptors.request.use(
  config => {
    if (!('Accept' in config.headers)) {
      config.headers['Accept'] = 'application/json;charset=UTF-8'
    }
    if (!('Content-Type' in config.headers)) {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    }
    // if (store.getters.token) {
    //   config.headers['Authorization'] = store.getters.token
    // }
    return config
  },
  error => {
    let __view_pop_error = error.response.config.__view_pop_error
    if (__view_pop_error) {
      ELMessage(`${__view_pop_error}${locale.t('58')}`, 'fail')
    }
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    let body = response.data
    let __use_raw_response_data = response.config.__use_raw_response_data
    if (__use_raw_response_data) {
      return body
    }
    let __view_pop_success = response.config.__view_pop_success
    let __view_pop_error = response.config.__view_pop_error
    let data = null
    let code = null
    let message = null
    if (body instanceof Object) {
      if ('state' in body) {
        code = body.state.code
        data = body.body
      } else if ('code' in body) {
        code = body.code
        data = body.data
        message = body.message
      }
    }
    if (code === null) {
      ELMessage(`${__view_pop_error}${locale.t('59')}`, 'error')
      return Promise.reject(body)
    }
    if (![0, 200].includes(code)) {
      // if (code === 3 || code === 401) {
      //   logout().then(() => {
      //     let currentPath = router.currentRoute.fullPath
      //     router.push(`/login?redirect=${currentPath}`)
      //   })
      // }
      if (__view_pop_error) {
        let errText = `${__view_pop_error}${locale.t('60')}${code}]`
        if (message) {
          errText = `${errText}[${message}]`
        }
        ELMessage(errText, 'error')
      }
      return Promise.reject(body)
    }
    if (__view_pop_success) {
      ELMessage(__view_pop_success, 'success')
    }
    return data
  },
  error => {
    let config = error.config ? error.config : error.request.config
    let __view_pop_error = config.__view_pop_error ? config.__view_pop_error : ''
    if (error.response === undefined) {
      ELMessage(`${__view_pop_error} [${error.message}]`, 'error')
    } else {
      let httpStatus = error.response && 'status' in error.response ? '' + error.response.status : 'none'
      if (httpStatus === '401') {
        const uiVarUrl = 'skynet/ui/variables'
        const loginUrl = 'skynet/auth/login'
        const reqUrl = config.url
        if (reqUrl.indexOf(uiVarUrl) > -1) {
          // 获取ui 变量失败，返回空对象
          return Promise.resolve({})
        } else if (reqUrl.indexOf(loginUrl) < 0) {
          /**
           * 非登陆请求，遇到401错误时跳转登陆界面
           */
          redirectToLogin()
        }
      } else if (![200, 201].includes(httpStatus)) {
        ELMessage(`${__view_pop_error} [HTTP ${httpStatus}]`, 'error')
      } else {
        if (__view_pop_error) {
          let msg = httpStatus !== 'none' ? `${__view_pop_error} [HTTP ${httpStatus}]` : __view_pop_error
          ELMessage(msg, 'error')
        }
      }
      return Promise.reject(error)
    }
  }
)
function redirectToLogin() {
  let currentPath = router.currentRoute.fullPath
  if (redirectLock || currentPath.startsWith('/login')) {
    return
  }
  redirectLock = true
  ELMessage(locale.t('61'), 'info')
  logout().then(() => {
    setTimeout(() => {
      window.open(`${window.location.origin}/#/login?redirect=${currentPath}`, '_self')
      redirectLock = false
    }, 2000)
  })
}
async function logout() {
  await store.dispatch('user/logout')
  return Promise.resolve()
}
export default service
