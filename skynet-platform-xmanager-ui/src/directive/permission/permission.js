import store from '@/store'

/**
 * 权限指令
 * 支持两种模式：
 * 1. v-permission="['admin']" - 没有权限时隐藏元素
 * 2. v-permission:disabled="['admin']" - 没有权限时禁用元素
 */
export default {
  inserted(el, binding, vnode) {
    checkPermission(el, binding, vnode)
  },
  update(el, binding, vnode) {
    checkPermission(el, binding, vnode)
  }
}

function checkPermission(el, binding, vnode) {
  const { value, arg } = binding
  const roles = store.getters && store.getters.roles

  if (value && value instanceof Array && value.length > 0) {
    const permissionRoles = value
    const hasPermission = roles.some(role => {
      return permissionRoles.includes(role)
    })

    if (!hasPermission) {
      if (arg === 'disabled') {
        // 禁用模式：设置disabled属性并添加样式
        el.disabled = true
        el.style.opacity = '0.5'
        el.style.cursor = 'not-allowed'
        el.style.pointerEvents = 'none'

        // 如果是Element UI的组件，需要特殊处理
        if (vnode.componentInstance) {
          vnode.componentInstance.disabled = true
        }

        // 添加title提示
        if (!el.title) {
          el.title = '您没有权限执行此操作'
        }
      } else {
        // 隐藏模式：移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      // 有权限时，确保元素是可用状态
      if (arg === 'disabled') {
        el.disabled = false
        el.style.opacity = ''
        el.style.cursor = ''
        el.style.pointerEvents = ''

        if (vnode.componentInstance) {
          vnode.componentInstance.disabled = false
        }

        // 移除权限相关的title
        if (el.title === '您没有权限执行此操作') {
          el.title = ''
        }
      }
    }
  } else {
    throw new Error(`need roles! Like v-permission="['admin','editor']" or v-permission:disabled="['admin']"`)
  }
}
