<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper">
      <draggable v-model="sortedViews" draggable=".tags-view-item">
        <router-link
          v-for="tag in sortedViews"
          ref="tag"
          :key="tag.path"
          :class="isActive(tag) ? 'active' : ''"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          tag="span"
          class="tags-view-item no-user-select"
          @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
          @contextmenu.prevent.native="openMenu(tag, $event)"
        >
          {{ tag.title }}
          <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
        </router-link>
      </draggable>
    </scroll-pane>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li v-show="isIframeTag(selectedTag)" @click="openInExplorerTab(selectedTag)">{{ $t('146') }}</li>
      <li @click="refreshSelectedTag(selectedTag)">{{ $t('147') }}</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">{{ $t('1134') }}</li>
      <li @click="closeOthersTags">{{ $t('148') }}</li>
      <li @click="closeAllTags(selectedTag)">{{ $t('149') }}</li>
    </ul>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ScrollPane from './ScrollPane'
import path from 'path'
import draggable from 'vuedraggable'
import EventBus from '@/components/event/EventBus'
import { getUrlWithToken } from '@/utils/auth'
export default {
  components: {
    ScrollPane,
    draggable
  },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      sequences: null
    }
  },
  created() {
    const __this = this
    EventBus.$on('close-tag', view => {
      __this.closeSelectedTag(view)
    })
  },
  computed: {
    sortedViews: {
      get() {
        return this.$store.state.tagsView.visitedViews
      },
      set(val) {
        this.$store.dispatch('tagsView/updateVisitedViews', val)
      }
    },
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    $route(val, oldVal) {
      this.addTags(val, oldVal)
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: {
              ...route.meta
            }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', {
            to: tag
          })
        }
      }
    },
    addTags(to, from) {
      if (!to) {
        to = this.$route
      }
      const { name } = to
      if (name) {
        this.$store.dispatch('tagsView/addView', {
          to,
          from
        })
      }
      return false
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      if (view.fullPath.startsWith('/external_link')) {
        let iframeUrl = view.query.url
        this.$store.dispatch('skynet/delCachedIframe', iframeUrl).then(() => {
          this.$nextTick(() => {
            this.$store.dispatch('skynet/updateVisitingIframe', iframeUrl)
            this.$store.dispatch('skynet/addCachedIframe', iframeUrl)
          })
        })
      } else {
        this.$store.dispatch('tagsView/forceDelCachedView', view).then(() => {
          const { fullPath } = view
          this.$nextTick(() => {
            this.$router.replace({
              path: '/redirect' + fullPath
            })
          })
        })
      }
    },
    closeSelectedTag(view) {
      let preView = this.findPreView(view)
      this.$store.dispatch('tagsView/delView', view)
      if (view.name === 'ExternalLink') {
        this.$store.dispatch('skynet/delCachedIframe', view.fullPath)
      }
      if (this.isActive(view)) {
        this.toPreView(preView)
      }
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOtherCachedIframes', this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag)
      this.moveToCurrentTag()
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(() => {
        if (this.affixTags.some(tag => tag.path === view.path)) {
          return
        }
        this.toPreView(null)
      })
    },
    findPreView(view) {
      let idxOfClosedView = this.visitedViews.findIndex(v => {
        return v.fullPath === view.fullPath
      })
      let preIdx = idxOfClosedView - 1
      if (preIdx < 0) {
        preIdx = this.visitedViews.length - 1
      }
      return preIdx < 0 ? null : this.visitedViews[preIdx]
    },
    toPreView(preView) {
      if (preView) {
        this.$router.push(preView.fullPath)
      } else {
        this.$router.push('/')
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft
      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      // this.top = e.clientY
      this.top = 35
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    isIframeTag(view) {
      return view && view.fullPath && view.fullPath.startsWith('/external_link')
    },
    openInExplorerTab(view) {
      if (view.query.url) {
        window.open(getUrlWithToken(view.query.url), '_blank')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tags-view-container {
  height: $tagsViewContainerHeight;
  width: 100%;
  background: #f5f6fa;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 13px !important;
      margin-left: 5px;
      margin-top: 4px;
      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        background-color: #367ae0;
        color: #fff;
        border-color: #367ae0;
        &::before {
          content: '';
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 2px;
        }
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>
<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
