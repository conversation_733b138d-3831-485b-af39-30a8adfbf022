<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
        <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import Hamburger from '@/components/Hamburger'
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
export default {
  components: {
    SidebarItem,
    Logo,
    Hamburger
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  created() {
    // console.log('permission_routes: %o', this.permission_routes)
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  }
}
</script>
<style lang="scss">
.el-menu-item.is-active {
  background: #29334d !important;
}
.hamburger-container {
  height: 40px;
  line-height: 40px;
  vertical-align: middle;
  cursor: pointer;
  svg {
    color: #909399;
    width: 14px;
    height: 14px;
    margin-top: 21px;
  }
  &:hover {
    svg {
      color: #909399;
    }
  }
}
.el-menu--inline {
  .nest-menu {
    a li {
      padding-left: 42px !important;
    }
  }
}
.scrollbar-wrapper {
  .el-menu-item {
  }
}
.el-submenu__title {
  padding-left: 16px !important;

  .iconfont {
    display: inline-block;
    width: 16px;
    margin-right: 10px;
  }
}
</style>
