<template>
  <div>
    <div id="myChart"></div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import echarts from 'echarts'
export default {
  data() {
    return {
      charts: '',
      optionData: [
        {
          value: 335,
          name: locale.t('77')
        },
        {
          value: 310,
          name: locale.t('78')
        }
      ]
    }
  },
  methods: {
    init(id) {
      this.charts = echarts.init(document.getElementById(id))
      this.charts.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: locale.t('79'),
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '30',
                  fontWeight: 'blod'
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: this.optionData
          }
        ]
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.init('myChart')
    })
  }
}
</script>
