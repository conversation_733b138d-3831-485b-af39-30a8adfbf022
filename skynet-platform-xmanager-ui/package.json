{"name": "skynet-ui-with-tpl", "version": "1.0.0", "description": "skynet webFront", "author": "doris", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@vue/composition-api": "^1.7.1", "axios": "0.18.1", "babel-plugin-prismjs": "2.0.1", "clipboard": "2.0.4", "codemirror": "^5.65.15", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.13.0", "file-saver": "2.0.1", "fuse.js": "3.4.4", "idiom-verification": "^0.1.0", "js-cookie": "2.2.0", "js-yaml": "^4.1.0", "jsencrypt": "^3.0.0-rc.1", "jsonlint": "1.6.3", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "prismjs": "^1.22.0", "sass": "1.58.3", "sass-loader": "7.1.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "showdown": "1.9.0", "sortablejs": "1.8.4", "tasksfile": "^5.1.0", "v-code-diff": "^0.3.12", "vue": "2.6.10", "vue-codemirror": "^4.0.6", "vue-drag-verify2": "1.1.0", "vue-echarts": "^5.0.0-beta.0", "vue-i18n": "^8.2.1", "vue-prism-editor": "^1.2.2", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "jwt-decode": "^4.0.0", "xlsx": "0.14.1", "xterm": "^4.19.0", "xterm-addon-attach": "^0.6.0", "xterm-addon-fit": "^0.5.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@types/codemirror": "^5.60.10", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vuefix": "^0.2.1", "html-webpack-plugin": "3.2.0", "js-base64": "3.5.2", "lint-staged": "8.1.5", "md5.js": "^1.3.4", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "^4.3.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "^1.13.2", "style-resources-loader": "1.3.3", "vue-cli-plugin-style-resources-loader": "0.1.4", "vue-particles": "^1.0.9", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.11", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}