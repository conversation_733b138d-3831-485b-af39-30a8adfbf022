# 权限控制系统使用说明

## 概述

本系统实现了基于角色的权限控制（RBAC），支持 `admin` 和 `viewer` 两种角色，并提供了灵活的前端权限控制指令。

## 角色说明

- **admin**: 管理员角色，拥有所有操作权限
- **viewer**: 查看者角色，只能查看数据，无法进行修改操作

## 权限控制指令

### 1. 隐藏模式 (v-permission)

当用户没有指定权限时，完全隐藏元素。

```vue
<!-- 只有admin用户能看到此按钮 -->
<el-button v-permission="['admin']">管理员专用按钮</el-button>

<!-- admin或editor用户都能看到 -->
<el-button v-permission="['admin', 'editor']">多角色按钮</el-button>
```

### 2. 禁用模式 (v-permission:disabled)

当用户没有指定权限时，显示但禁用元素，并添加视觉提示。

```vue
<!-- 非admin用户可以看到但无法点击 -->
<el-button v-permission:disabled="['admin']" @click="handleSave">保存</el-button>

<!-- 支持多角色 -->
<el-button v-permission:disabled="['admin', 'editor']" @click="handleEdit">编辑</el-button>
```

## 后端API

### 获取用户信息接口

```
GET /skynet/auth/userinfo
```

返回格式：
```json
{
  "code": 200,
  "data": {
    "username": "admin",
    "name": "Administrator", 
    "introduction": "管理员",
    "roles": ["admin"]
  }
}
```

## 前端实现

### 1. 用户信息获取

用户登录后，系统会：
1. 首先尝试从后端API获取用户角色信息
2. 如果API调用失败，从JWT token中解析角色信息
3. 将角色信息存储到Vuex store中

### 2. 权限检查流程

```javascript
// 在组件中检查权限
import checkPermission from '@/utils/permission'

export default {
  methods: {
    handleOperation() {
      if (checkPermission(['admin'])) {
        // 执行需要admin权限的操作
      } else {
        this.$message.error('您没有权限执行此操作')
      }
    }
  }
}
```

## 使用示例

### 1. 基本按钮权限控制

```vue
<template>
  <div>
    <!-- 隐藏模式：非admin用户看不到 -->
    <el-button type="danger" v-permission="['admin']" @click="deleteAll">
      批量删除
    </el-button>
    
    <!-- 禁用模式：非admin用户看到但无法操作 -->
    <el-button type="primary" v-permission:disabled="['admin']" @click="save">
      保存配置
    </el-button>
  </div>
</template>
```

### 2. 表格操作权限控制

```vue
<el-table :data="tableData">
  <el-table-column label="操作">
    <template slot-scope="scope">
      <el-button size="mini" @click="view(scope.row)">查看</el-button>
      <el-button 
        size="mini" 
        type="primary" 
        v-permission:disabled="['admin']" 
        @click="edit(scope.row)">
        编辑
      </el-button>
      <el-button 
        size="mini" 
        type="danger" 
        v-permission:disabled="['admin']" 
        @click="delete(scope.row)">
        删除
      </el-button>
    </template>
  </el-table-column>
</el-table>
```

### 3. 表单权限控制

```vue
<el-form :model="form">
  <el-form-item label="配置名称">
    <el-input v-model="form.name" :disabled="!isAdmin"></el-input>
  </el-form-item>
  
  <el-form-item>
    <el-button type="primary" v-permission:disabled="['admin']" @click="submit">
      提交
    </el-button>
  </el-form-item>
</el-form>
```

## 测试页面

访问 `/permission-test` 页面可以测试权限控制功能，该页面提供了：
- 角色信息显示
- 隐藏模式和禁用模式的对比演示
- 常见操作按钮的权限控制示例
- 表格操作权限控制示例
- 角色切换测试功能（仅用于开发测试）

## 注意事项

1. **安全性**: 前端权限控制仅用于用户体验优化，真正的权限验证必须在后端进行
2. **角色格式**: 角色名称统一使用小写，如 `admin`、`viewer`
3. **JWT Token**: 系统会自动处理JWT token中的角色信息，去掉 `ROLE_` 前缀
4. **错误处理**: 当权限检查失败时，系统会提供友好的错误提示
5. **性能**: 权限指令支持动态更新，当用户角色变化时会自动重新检查权限

## 开发建议

1. 对于重要的操作（删除、修改等），建议使用禁用模式而不是隐藏模式
2. 在组件的methods中也要进行权限检查，不要仅依赖指令
3. 为禁用的按钮提供清晰的提示信息
4. 定期检查和更新权限控制逻辑，确保与业务需求一致
